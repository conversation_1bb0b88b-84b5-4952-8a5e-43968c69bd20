#!/usr/bin/env python3
"""
Тест для проверки работоспособности OpenAI API ключа
"""

import asyncio
from openai import OpenAI
from jarvis.config import config
from jarvis.core.logger import jarvis_logger

async def test_openai_api():
    """Тестирование OpenAI API"""
    try:
        jarvis_logger.info("🔑 Testing OpenAI API key...")
        jarvis_logger.info(f"API Key: {config.openai_api_key[:20]}...")
        
        client = OpenAI(api_key=config.openai_api_key)
        
        # Тестовый запрос
        response = await asyncio.to_thread(
            client.chat.completions.create,
            model="gpt-3.5-turbo",
            messages=[
                {"role": "user", "content": "Привет! Скажи 'API работает' если ты меня слышишь."}
            ],
            max_tokens=20
        )
        
        if response and response.choices:
            jarvis_logger.info("✅ OpenAI API connection successful!")
            jarvis_logger.info(f"Response: {response.choices[0].message.content}")
            jarvis_logger.info(f"Model used: {response.model}")
            jarvis_logger.info(f"Tokens used: {response.usage.total_tokens}")
            return True
        else:
            jarvis_logger.error("❌ Empty response from OpenAI")
            return False
            
    except Exception as e:
        jarvis_logger.error(f"❌ OpenAI API test failed: {e}")
        return False

async def main():
    """Главная функция теста"""
    jarvis_logger.info("🚀 Starting OpenAI API test...")
    
    success = await test_openai_api()
    
    if success:
        jarvis_logger.info("🎉 API test completed successfully!")
    else:
        jarvis_logger.error("💥 API test failed!")
    
    return success

if __name__ == "__main__":
    asyncio.run(main())
