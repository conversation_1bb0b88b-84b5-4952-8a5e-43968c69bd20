#!/usr/bin/env python3
"""
Тест модуля управления приложениями
"""

import asyncio
from jarvis.modules.app_manager import AppManager
from jarvis.modules.command_processor import LocalCommandProcessor
from jarvis.core.logger import jarvis_logger

async def test_app_manager():
    """Тестирование модуля управления приложениями"""
    jarvis_logger.info("🚀 Starting App Manager test...")
    
    # Инициализируем модули
    app_manager = AppManager()
    command_processor = LocalCommandProcessor()
    
    # Инициализация
    if not await app_manager.initialize():
        jarvis_logger.error("❌ Failed to initialize App Manager")
        return False
    
    if not await command_processor.initialize():
        jarvis_logger.error("❌ Failed to initialize Command Processor")
        return False
    
    jarvis_logger.info("✅ Modules initialized successfully")
    
    # Тест 1: Список запущенных приложений
    jarvis_logger.info("\n📱 Test 1: Listing running applications...")
    result = await app_manager.list_running_applications()
    if result.success:
        apps = result.data.get("apps", [])
        jarvis_logger.info(f"✅ Found {len(apps)} running applications:")
        for app in apps[:5]:  # Показываем первые 5
            jarvis_logger.info(f"   • {app}")
        if len(apps) > 5:
            jarvis_logger.info(f"   ... and {len(apps) - 5} more")
    else:
        jarvis_logger.error(f"❌ Failed to list apps: {result.message}")
    
    # Тест 2: Парсинг команд
    jarvis_logger.info("\n🧠 Test 2: Command parsing...")
    test_commands = [
        "открой калькулятор",
        "запусти сафари", 
        "закрой терминал",
        "open calculator",
        "launch safari"
    ]
    
    for cmd in test_commands:
        parsed = command_processor.parse_command(cmd)
        jarvis_logger.info(f"   '{cmd}' -> {parsed['intent']} (entities: {parsed['entities']})")
    
    # Тест 3: Поиск приложения
    jarvis_logger.info("\n🔍 Test 3: Finding applications...")
    test_apps = ["Calculator", "Safari", "Finder", "Terminal"]
    
    for app in test_apps:
        found = await app_manager.find_application(app)
        if found:
            jarvis_logger.info(f"   ✅ Found: {app} -> {found}")
        else:
            jarvis_logger.info(f"   ❌ Not found: {app}")
    
    # Тест 4: Открытие приложения (безопасный тест с Calculator)
    jarvis_logger.info("\n🚀 Test 4: Opening Calculator...")
    result = await app_manager.open_application("Calculator")
    if result.success:
        jarvis_logger.info(f"✅ {result.message}")
        
        # Ждем немного и закрываем
        await asyncio.sleep(2)
        jarvis_logger.info("🔴 Closing Calculator...")
        close_result = await app_manager.close_application("Calculator")
        if close_result.success:
            jarvis_logger.info(f"✅ {close_result.message}")
        else:
            jarvis_logger.error(f"❌ {close_result.message}")
    else:
        jarvis_logger.error(f"❌ {result.message}")
    
    # Очистка
    await app_manager.cleanup()
    await command_processor.cleanup()
    
    jarvis_logger.info("\n🏁 App Manager test completed!")
    return True

async def main():
    """Главная функция теста"""
    try:
        await test_app_manager()
    except KeyboardInterrupt:
        jarvis_logger.info("🛑 Test interrupted by user")
    except Exception as e:
        jarvis_logger.error(f"💥 Test failed with error: {e}")

if __name__ == "__main__":
    asyncio.run(main())
