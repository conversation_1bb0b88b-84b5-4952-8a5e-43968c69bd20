#!/usr/bin/env python3
"""
Тест голосового модуля Jarvis
"""

import asyncio
from jarvis.modules.voice import VoiceModule
from jarvis.core.logger import jarvis_logger

async def test_voice_command(command: str):
    """Тестовый обработчик голосовых команд"""
    jarvis_logger.info(f"🎤 Test received command: '{command}'")
    
    if "привет" in command:
        jarvis_logger.info("👋 Привет! Тест прошел успешно!")
    elif "тест" in command:
        jarvis_logger.info("✅ Голосовой модуль работает!")
    elif "стоп" in command:
        jarvis_logger.info("🛑 Остановка теста...")
        return False
    else:
        jarvis_logger.info(f"🤔 Неизвестная команда: '{command}'")
    
    return True

async def main():
    """Главная функция теста"""
    jarvis_logger.info("🚀 Starting Voice Module test...")
    
    # Создаем и инициализируем голосовой модуль
    voice_module = VoiceModule()
    voice_module.set_command_callback(test_voice_command)
    
    success = await voice_module.initialize()
    if not success:
        jarvis_logger.error("❌ Failed to initialize Voice Module")
        return
    
    jarvis_logger.info("✅ Voice Module initialized successfully!")
    jarvis_logger.info("🎤 Say 'jarvis' followed by a command...")
    jarvis_logger.info("💡 Try: 'jarvis привет', 'jarvis тест', 'jarvis стоп'")
    jarvis_logger.info("⏹️  Press Ctrl+C to stop")
    
    # Запускаем прослушивание
    await voice_module.start_listening()
    
    try:
        # Ждем команд
        while True:
            await asyncio.sleep(1)
    except KeyboardInterrupt:
        jarvis_logger.info("🛑 Test interrupted by user")
    finally:
        await voice_module.cleanup()
        jarvis_logger.info("🏁 Voice Module test completed")

if __name__ == "__main__":
    asyncio.run(main())
