#!/usr/bin/env python3
"""
Полный тест системы Jarvis с голосовыми ответами
"""

import asyncio
from jarvis.modules.voice import VoiceModule
from jarvis.modules.command_processor import LocalCommandProcessor
from jarvis.modules.app_manager import AppManager
from jarvis.modules.tts import TTSModule
from jarvis.core.logger import jarvis_logger

async def simulate_full_conversation():
    """Симуляция полного разговора с Jarvis"""
    jarvis_logger.info("🚀 Starting Full Jarvis System Simulation...")
    
    # Инициализируем все модули
    voice_module = VoiceModule()
    command_processor = LocalCommandProcessor()
    app_manager = AppManager()
    tts_module = TTSModule()
    
    # Инициализация
    voice_module.is_initialized = True
    await command_processor.initialize()
    await app_manager.initialize()
    await tts_module.initialize()
    
    jarvis_logger.info("✅ All modules initialized")
    
    async def process_command(command: str):
        """Обработка команды с голосовым ответом (копия из main.py)"""
        jarvis_logger.info(f"🎤 Processing voice command: '{command}'")
        
        try:
            # Парсим команду
            parsed_result = await command_processor.execute(command)
            if not parsed_result.success:
                jarvis_logger.error(f"Failed to parse command: {parsed_result.error}")
                return
            
            parsed_command = parsed_result.data
            intent = parsed_command["intent"]
            entities = parsed_command["entities"]
            
            jarvis_logger.info(f"🧠 Detected intent: {intent}")
            
            # Обрабатываем команды с голосовыми ответами
            if intent == "greeting":
                response = "Привет! Я ваш персональный помощник Джарвис!"
                jarvis_logger.info(f"👋 {response}")
                await tts_module.speak(response)
                
            elif intent == "test":
                response = "Все системы работают нормально!"
                jarvis_logger.info(f"✅ {response}")
                await tts_module.speak(response)
                
            elif intent == "help":
                response = "Доступные команды: привет, открой приложение, закрой приложение, тест, помощь"
                jarvis_logger.info("💡 Показываю справку...")
                await tts_module.speak(response)
                
            elif intent == "app_open" and entities:
                app_name = command_processor.extract_app_name(entities[0])
                jarvis_logger.info(f"🚀 Opening application: {app_name}")
                result = await app_manager.open_application(app_name)
                if result.success:
                    response = f"Открываю {app_name}"
                    jarvis_logger.info(f"✅ {result.message}")
                    await tts_module.speak(response)
                else:
                    response = f"Не могу найти приложение {app_name}"
                    jarvis_logger.error(f"❌ {result.message}")
                    await tts_module.speak(response)
                    
            elif intent == "app_close" and entities:
                app_name = command_processor.extract_app_name(entities[0])
                jarvis_logger.info(f"🔴 Closing application: {app_name}")
                result = await app_manager.close_application(app_name)
                if result.success:
                    response = f"Закрываю {app_name}"
                    jarvis_logger.info(f"✅ {result.message}")
                    await tts_module.speak(response)
                else:
                    response = f"Не могу закрыть {app_name}"
                    jarvis_logger.error(f"❌ {result.message}")
                    await tts_module.speak(response)
                    
            elif intent == "unknown":
                response = "Извините, я не понял команду. Скажите помощь для списка команд."
                jarvis_logger.info(f"🤔 Команда '{command}' не распознана")
                await tts_module.speak(response)
                
            else:
                response = f"Команда {intent} пока не реализована"
                jarvis_logger.info(f"🔧 Команда '{intent}' пока не реализована")
                await tts_module.speak(response)
                
        except Exception as e:
            jarvis_logger.error(f"Error processing command: {e}")
    
    # Симулируем разговор
    conversation = [
        "привет",
        "тест",
        "открой сафари",
        "помощь",
        "закрой сафари",
        "неизвестная команда",
    ]
    
    jarvis_logger.info("\n🎭 Starting conversation simulation...")
    jarvis_logger.info("🔊 You should hear Jarvis speaking!")
    
    for i, command in enumerate(conversation, 1):
        jarvis_logger.info(f"\n--- Conversation {i}: User says '{command}' ---")
        
        # Симулируем wake word detection
        voice_module.wake_word_detected = True
        voice_module._process_voice_input_sync(command)
        
        # Получаем команду из очереди
        received_command = voice_module.get_command()
        if received_command:
            await process_command(received_command)
        else:
            jarvis_logger.error("❌ Command not received from queue")
        
        # Пауза между командами
        await asyncio.sleep(3)
    
    # Очистка
    await voice_module.cleanup()
    await command_processor.cleanup()
    await app_manager.cleanup()
    await tts_module.cleanup()
    
    jarvis_logger.info("\n🏁 Full System Simulation completed!")
    jarvis_logger.info("🎉 Jarvis is now fully functional with voice responses!")

async def main():
    """Главная функция теста"""
    try:
        await simulate_full_conversation()
    except KeyboardInterrupt:
        jarvis_logger.info("🛑 Test interrupted by user")
    except Exception as e:
        jarvis_logger.error(f"💥 Test failed with error: {e}")

if __name__ == "__main__":
    asyncio.run(main())
