#!/usr/bin/env python3
"""
Тест голосовых команд без микрофона (симуляция)
"""

import asyncio
from jarvis.modules.voice import VoiceModule
from jarvis.modules.command_processor import LocalCommandProcessor
from jarvis.modules.app_manager import AppManager
from jarvis.core.logger import jarvis_logger

async def simulate_voice_commands():
    """Симуляция голосовых команд для тестирования"""
    jarvis_logger.info("🚀 Starting Voice Commands Simulation...")
    
    # Инициализируем модули
    voice_module = VoiceModule()
    command_processor = LocalCommandProcessor()
    app_manager = AppManager()
    
    # Инициализация (без микрофона)
    voice_module.is_initialized = True
    await command_processor.initialize()
    await app_manager.initialize()
    
    jarvis_logger.info("✅ Modules initialized")
    
    # Симулируем команды
    test_commands = [
        "привет",
        "открой калькулятор", 
        "запусти сафари",
        "помощь",
        "тест"
    ]
    
    async def process_command(command: str):
        """Обработка команды (копия из main.py)"""
        jarvis_logger.info(f"🎤 Processing voice command: '{command}'")
        
        try:
            # Парсим команду
            parsed_result = await command_processor.execute(command)
            if not parsed_result.success:
                jarvis_logger.error(f"Failed to parse command: {parsed_result.error}")
                return
            
            parsed_command = parsed_result.data
            intent = parsed_command["intent"]
            entities = parsed_command["entities"]
            
            jarvis_logger.info(f"🧠 Detected intent: {intent}")
            
            # Обрабатываем команды
            if intent == "greeting":
                jarvis_logger.info("👋 Привет! Я ваш персональный помощник Jarvis!")
                
            elif intent == "test":
                jarvis_logger.info("✅ Все системы работают нормально!")
                
            elif intent == "help":
                jarvis_logger.info("💡 Доступные команды:")
                jarvis_logger.info("   • 'Привет' - поприветствовать")
                jarvis_logger.info("   • 'Открой [приложение]' - запустить приложение")
                jarvis_logger.info("   • 'Тест' - проверить работу")
                
            elif intent == "app_open" and entities:
                app_name = command_processor.extract_app_name(entities[0])
                jarvis_logger.info(f"🚀 Opening application: {app_name}")
                result = await app_manager.open_application(app_name)
                if result.success:
                    jarvis_logger.info(f"✅ {result.message}")
                else:
                    jarvis_logger.error(f"❌ {result.message}")
                    
            elif intent == "unknown":
                jarvis_logger.info(f"🤔 Команда '{command}' не распознана")
                
            else:
                jarvis_logger.info(f"🔧 Команда '{intent}' пока не реализована")
                
        except Exception as e:
            jarvis_logger.error(f"Error processing command: {e}")
    
    # Тестируем команды
    for i, command in enumerate(test_commands, 1):
        jarvis_logger.info(f"\n--- Test {i}: '{command}' ---")
        
        # Симулируем wake word detection
        voice_module.wake_word_detected = True
        voice_module._process_voice_input_sync(command)
        
        # Получаем команду из очереди
        received_command = voice_module.get_command()
        if received_command:
            await process_command(received_command)
        else:
            jarvis_logger.error("❌ Command not received from queue")
        
        await asyncio.sleep(1)
    
    # Очистка
    await voice_module.cleanup()
    await command_processor.cleanup()
    await app_manager.cleanup()
    
    jarvis_logger.info("\n🏁 Voice Commands Simulation completed!")

async def main():
    """Главная функция теста"""
    try:
        await simulate_voice_commands()
    except KeyboardInterrupt:
        jarvis_logger.info("🛑 Test interrupted by user")
    except Exception as e:
        jarvis_logger.error(f"💥 Test failed with error: {e}")

if __name__ == "__main__":
    asyncio.run(main())
