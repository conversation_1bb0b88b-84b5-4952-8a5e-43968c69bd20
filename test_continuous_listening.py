#!/usr/bin/env python3
"""
Тест непрерывного прослушивания
"""

import asyncio
import time
from jarvis.modules.voice import VoiceModule
from jarvis.core.logger import jarvis_logger

async def test_continuous_listening():
    """Тест непрерывного прослушивания"""
    jarvis_logger.info("🚀 Starting Continuous Listening Test...")
    
    # Создаем голосовой модуль
    voice_module = VoiceModule()
    
    # Инициализируем
    success = await voice_module.initialize()
    if not success:
        jarvis_logger.error("❌ Failed to initialize voice module")
        return
    
    jarvis_logger.info("✅ Voice module initialized")
    jarvis_logger.info("🎤 Starting continuous listening...")
    jarvis_logger.info("📢 Try saying:")
    jarvis_logger.info("   • 'Джарвис' (to activate)")
    jarvis_logger.info("   • Then any command like 'привет', 'тест', 'открой сафари'")
    jarvis_logger.info("🔍 Monitoring for 60 seconds...")
    
    # Запускаем прослушивание
    await voice_module.start_listening()
    
    # Мониторим в течение 60 секунд
    start_time = time.time()
    command_count = 0
    last_status_time = time.time()
    
    try:
        while time.time() - start_time < 60:  # 60 секунд теста
            # Проверяем очередь команд
            command = voice_module.get_command()
            if command:
                command_count += 1
                jarvis_logger.info(f"🎯 COMMAND #{command_count}: '{command}'")
                
                # Простая обработка
                if "привет" in command.lower():
                    jarvis_logger.info("👋 Hello detected!")
                elif "тест" in command.lower():
                    jarvis_logger.info("🧪 Test command detected!")
                elif "открой" in command.lower():
                    jarvis_logger.info("🚀 Open command detected!")
                elif "закрой" in command.lower():
                    jarvis_logger.info("🔴 Close command detected!")
                else:
                    jarvis_logger.info(f"🤔 Unknown command: {command}")
            
            # Показываем статус каждые 10 секунд
            current_time = time.time()
            if current_time - last_status_time > 10:
                elapsed = int(current_time - start_time)
                remaining = 60 - elapsed
                jarvis_logger.info(f"📊 Status: {elapsed}s elapsed, {remaining}s remaining. Commands: {command_count}")
                jarvis_logger.info(f"🔊 Wake word state: {voice_module.wake_word_detected}")
                last_status_time = current_time
            
            await asyncio.sleep(0.1)
            
    except KeyboardInterrupt:
        jarvis_logger.info("🛑 Test interrupted by user")
    finally:
        await voice_module.cleanup()
        jarvis_logger.info(f"🏁 Test completed. Total commands received: {command_count}")
        
        if command_count > 0:
            jarvis_logger.info("✅ Continuous listening is working!")
        else:
            jarvis_logger.warning("⚠️ No commands received. Check microphone and try speaking louder.")

async def main():
    """Главная функция теста"""
    try:
        await test_continuous_listening()
    except Exception as e:
        jarvis_logger.error(f"💥 Test failed with error: {e}")

if __name__ == "__main__":
    asyncio.run(main())
