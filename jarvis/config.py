"""
Конфигурация для Jarvis AI Assistant
"""

import os
from pathlib import Path
from typing import Optional
from pydantic import Field
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

# Загружаем переменные окружения
load_dotenv()

class JarvisConfig(BaseSettings):
    """Основная конфигурация Jarvis"""
    
    # API ключи
    openai_api_key: str = Field(..., env="OPENAI_API_KEY")
    
    # Основные настройки
    jarvis_name: str = Field("Jarvis", env="JARVIS_NAME")
    wake_word: str = Field("jarvis", env="WAKE_WORD")
    language: str = Field("ru-RU", env="LANGUAGE")
    
    # Настройки голоса
    voice_recognition_timeout: int = Field(30, env="VOICE_RECOGNITION_TIMEOUT")
    voice_recognition_phrase_timeout: float = Field(5.0, env="VOICE_RECOGNITION_PHRASE_TIMEOUT")
    
    # Логирование
    log_level: str = Field("INFO", env="LOG_LEVEL")
    log_file: str = Field("logs/jarvis.log", env="LOG_FILE")
    
    # Приложения
    default_browser: str = Field("Safari", env="DEFAULT_BROWSER")
    telegram_app_name: str = Field("Telegram", env="TELEGRAM_APP_NAME")
    
    # Пути
    project_root: Path = Field(default_factory=lambda: Path(__file__).parent.parent)
    logs_dir: Path = Field(default_factory=lambda: Path(__file__).parent.parent / "logs")
    
    class Config:
        env_file = ".env"
        case_sensitive = False

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # Создаем директорию для логов если её нет
        self.logs_dir.mkdir(exist_ok=True)

# Глобальный экземпляр конфигурации
config = JarvisConfig()
