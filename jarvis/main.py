"""
Главн<PERSON>й модуль Jarvis AI Assistant
"""

import asyncio
import signal
import sys
from typing import List
from jarvis.core.logger import jarvis_logger
from jarvis.core.base import BaseModule
from jarvis.config import config
from jarvis.modules.voice import VoiceModule
from jarvis.modules.command_processor import LocalCommandProcessor
from jarvis.modules.app_manager import AppManager

class JarvisCore:
    """Основной класс Jarvis AI Assistant"""
    
    def __init__(self):
        self.logger = jarvis_logger
        self.modules: List[BaseModule] = []
        self.is_running = False
        
    async def initialize(self):
        """Инициализация Jarvis"""
        self.logger.info(f"Initializing {config.jarvis_name} AI Assistant...")
        
        # Инициализируем модули
        self.voice_module = VoiceModule()
        self.command_processor = LocalCommandProcessor()
        self.app_manager = AppManager()



        self.modules = [
            self.voice_module,
            self.command_processor,
            self.app_manager,
            # TODO: Добавим остальные модули по мере их создания
            # TelegramModule(),
            # SystemModule(),
            # FileModule(),
            # WebModule()
        ]
        
        # Инициализируем все модули
        for module in self.modules:
            try:
                success = await module.initialize()
                if success:
                    self.logger.info(f"Module {module.name} initialized successfully")
                else:
                    self.logger.error(f"Failed to initialize module {module.name}")
            except Exception as e:
                self.logger.error(f"Error initializing module {module.name}: {e}")
        
        self.logger.info(f"{config.jarvis_name} initialization complete!")
    
    async def start(self):
        """Запуск Jarvis"""
        await self.initialize()
        self.is_running = True
        
        self.logger.info(f"{config.jarvis_name} is now listening...")
        self.logger.info(f"Say '{config.wake_word}' to activate")

        # Запускаем голосовое прослушивание
        if self.voice_module.is_initialized:
            await self.voice_module.start_listening()

        try:
            while self.is_running:
                # Проверяем очередь голосовых команд
                if self.voice_module.is_initialized:
                    command = self.voice_module.get_command()
                    if command:
                        await self._handle_voice_command(command)

                await asyncio.sleep(0.1)

        except KeyboardInterrupt:
            self.logger.info("Received interrupt signal")
        finally:
            await self.shutdown()
    
    async def shutdown(self):
        """Завершение работы Jarvis"""
        self.logger.info(f"Shutting down {config.jarvis_name}...")
        self.is_running = False
        
        # Очищаем ресурсы модулей
        for module in self.modules:
            try:
                await module.cleanup()
            except Exception as e:
                self.logger.error(f"Error cleaning up module {module.name}: {e}")
        
        self.logger.info(f"{config.jarvis_name} shutdown complete")

    async def _handle_voice_command(self, command: str):
        """Обработка голосовых команд"""
        self.logger.info(f"🎤 Processing voice command: '{command}'")

        try:
            # Парсим команду через локальный процессор
            parsed_result = await self.command_processor.execute(command)
            if not parsed_result.success:
                self.logger.error(f"Failed to parse command: {parsed_result.error}")
                return

            parsed_command = parsed_result.data
            intent = parsed_command["intent"]
            entities = parsed_command["entities"]

            self.logger.info(f"🧠 Detected intent: {intent}")

            # Обрабатываем команды по намерениям
            if intent == "greeting":
                self.logger.info("👋 Привет! Я ваш персональный помощник Jarvis!")

            elif intent == "goodbye":
                self.logger.info("👋 До свидания! Завершаю работу...")
                self.is_running = False

            elif intent == "test":
                self.logger.info("✅ Все системы работают нормально!")

            elif intent == "stop":
                self.logger.info("🛑 Остановка по команде пользователя")
                self.is_running = False

            elif intent == "help":
                self._show_help()

            elif intent == "app_open" and entities:
                app_name = self.command_processor.extract_app_name(entities[0])
                self.logger.info(f"🚀 Opening application: {app_name}")
                result = await self.app_manager.open_application(app_name)
                if result.success:
                    self.logger.info(f"✅ {result.message}")
                else:
                    self.logger.error(f"❌ {result.message}")

            elif intent == "app_close" and entities:
                app_name = self.command_processor.extract_app_name(entities[0])
                self.logger.info(f"🔴 Closing application: {app_name}")
                result = await self.app_manager.close_application(app_name)
                if result.success:
                    self.logger.info(f"✅ {result.message}")
                else:
                    self.logger.error(f"❌ {result.message}")

            elif intent == "unknown":
                self.logger.info(f"🤔 Команда '{command}' не распознана")
                self._show_help()

            else:
                self.logger.info(f"🔧 Команда '{intent}' пока не реализована")

        except Exception as e:
            self.logger.error(f"Error processing voice command: {e}")

    def _show_help(self):
        """Показать справку по командам"""
        self.logger.info("💡 Доступные команды:")
        self.logger.info("   • 'Привет' - поприветствовать")
        self.logger.info("   • 'Открой [приложение]' - запустить приложение")
        self.logger.info("   • 'Закрой [приложение]' - закрыть приложение")
        self.logger.info("   • 'Тест' - проверить работу")
        self.logger.info("   • 'Помощь' - показать эту справку")
        self.logger.info("   • 'Стоп' - завершить работу")

async def main():
    """Главная функция"""
    jarvis = JarvisCore()
    
    # Обработка сигналов для корректного завершения
    def signal_handler(signum, frame):
        jarvis.logger.info(f"Received signal {signum}")
        jarvis.is_running = False
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    await jarvis.start()

if __name__ == "__main__":
    asyncio.run(main())
