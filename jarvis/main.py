"""
Главн<PERSON>й модуль Jarvis AI Assistant
"""

import asyncio
import signal
import sys
from typing import List
from jarvis.core.logger import jarvis_logger
from jarvis.core.base import BaseModule
from jarvis.config import config
from jarvis.modules.voice import VoiceModule

class JarvisCore:
    """Основной кла<PERSON><PERSON> Jarvis AI Assistant"""
    
    def __init__(self):
        self.logger = jarvis_logger
        self.modules: List[BaseModule] = []
        self.is_running = False
        
    async def initialize(self):
        """Инициализация Jarvis"""
        self.logger.info(f"Initializing {config.jarvis_name} AI Assistant...")
        
        # Инициализируем модули
        self.voice_module = VoiceModule()
        self.voice_module.set_command_callback(self._handle_voice_command)

        self.modules = [
            self.voice_module,
            # TODO: Добавим остальные модули по мере их создания
            # AppModule(),
            # CommandModule(),
            # TelegramModule(),
            # SystemModule(),
            # FileModule(),
            # WebModule()
        ]
        
        # Инициализируем все модули
        for module in self.modules:
            try:
                success = await module.initialize()
                if success:
                    self.logger.info(f"Module {module.name} initialized successfully")
                else:
                    self.logger.error(f"Failed to initialize module {module.name}")
            except Exception as e:
                self.logger.error(f"Error initializing module {module.name}: {e}")
        
        self.logger.info(f"{config.jarvis_name} initialization complete!")
    
    async def start(self):
        """Запуск Jarvis"""
        await self.initialize()
        self.is_running = True
        
        self.logger.info(f"{config.jarvis_name} is now listening...")
        self.logger.info(f"Say '{config.wake_word}' to activate")

        # Запускаем голосовое прослушивание
        if self.voice_module.is_initialized:
            await self.voice_module.start_listening()

        try:
            while self.is_running:
                await asyncio.sleep(0.1)

        except KeyboardInterrupt:
            self.logger.info("Received interrupt signal")
        finally:
            await self.shutdown()
    
    async def shutdown(self):
        """Завершение работы Jarvis"""
        self.logger.info(f"Shutting down {config.jarvis_name}...")
        self.is_running = False
        
        # Очищаем ресурсы модулей
        for module in self.modules:
            try:
                await module.cleanup()
            except Exception as e:
                self.logger.error(f"Error cleaning up module {module.name}: {e}")
        
        self.logger.info(f"{config.jarvis_name} shutdown complete")

    async def _handle_voice_command(self, command: str):
        """Обработка голосовых команд"""
        self.logger.info(f"🎤 Processing voice command: '{command}'")

        # Простая обработка команд для тестирования
        if "привет" in command or "hello" in command:
            self.logger.info("👋 Привет! Я слышу вас!")
        elif "стоп" in command or "stop" in command or "выход" in command:
            self.logger.info("👋 До свидания!")
            self.is_running = False
        elif "тест" in command or "test" in command:
            self.logger.info("✅ Голосовые команды работают!")
        else:
            self.logger.info(f"🤔 Команда '{command}' пока не поддерживается")
            self.logger.info("💡 Попробуйте: 'привет', 'тест', 'стоп'")

async def main():
    """Главная функция"""
    jarvis = JarvisCore()
    
    # Обработка сигналов для корректного завершения
    def signal_handler(signum, frame):
        jarvis.logger.info(f"Received signal {signum}")
        jarvis.is_running = False
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    await jarvis.start()

if __name__ == "__main__":
    asyncio.run(main())
