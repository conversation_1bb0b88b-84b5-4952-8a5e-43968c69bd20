"""
Модуль синтеза речи (Text-to-Speech) для Jarvis
"""

import asyncio
import subprocess
from typing import Optional
from jarvis.core.base import BaseModule, CommandResult
from jarvis.config import config

class TTSModule(BaseModule):
    """Модуль для синтеза речи (голосовых ответов)"""
    
    def __init__(self):
        super().__init__("TTSModule")
        self.voice_name = "Milena"  # Русский голос на macOS
        self.speaking_rate = 200    # Скорость речи
        self.is_speaking = False
    
    async def initialize(self) -> bool:
        """Инициализация модуля TTS"""
        try:
            self.logger.info("Initializing TTS Module...")
            
            # Проверяем доступность команды say
            result = await self._test_say_command()
            if not result:
                self.logger.warning("⚠️ TTS may not work properly")
                return False
            
            # Получаем список доступных голосов
            await self._get_available_voices()
            
            self.is_initialized = True
            self.logger.info("✅ TTS Module initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize TTS Module: {e}")
            return False
    
    async def _test_say_command(self) -> bool:
        """Тестирование команды say"""
        try:
            # Тестируем простую команду say
            process = await asyncio.create_subprocess_exec(
                'say', '-v', '?',
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()

            if process.returncode == 0:
                self.logger.info("✅ macOS 'say' command is available")
                return True
            else:
                self.logger.error("❌ macOS 'say' command not available")
                return False

        except Exception as e:
            self.logger.error(f"Error testing say command: {e}")
            return False
    
    async def _get_available_voices(self):
        """Получение списка доступных голосов"""
        try:
            process = await asyncio.create_subprocess_exec(
                'say', '-v', '?',
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                voices = stdout.decode('utf-8').strip().split('\n')
                russian_voices = []
                english_voices = []
                
                for voice_line in voices:
                    if 'ru_RU' in voice_line or 'Russian' in voice_line:
                        voice_name = voice_line.split()[0]
                        russian_voices.append(voice_name)
                    elif 'en_US' in voice_line or 'English' in voice_line:
                        voice_name = voice_line.split()[0]
                        english_voices.append(voice_name)
                
                self.logger.info(f"Available Russian voices: {russian_voices}")
                self.logger.info(f"Available English voices: {english_voices[:3]}...")  # Показываем первые 3
                
                # Выбираем лучший русский голос
                if 'Milena' in russian_voices:
                    self.voice_name = 'Milena'
                elif 'Yuri' in russian_voices:
                    self.voice_name = 'Yuri'
                elif russian_voices:
                    self.voice_name = russian_voices[0]
                else:
                    self.voice_name = 'Alex'  # Английский по умолчанию
                
                self.logger.info(f"Selected voice: {self.voice_name}")
                
        except Exception as e:
            self.logger.error(f"Error getting voices: {e}")
    
    async def speak(self, text: str, voice: Optional[str] = None, rate: Optional[int] = None) -> CommandResult:
        """Произнести текст"""
        if self.is_speaking:
            self.logger.warning("Already speaking, skipping...")
            return CommandResult(False, "Already speaking")
        
        try:
            self.is_speaking = True
            selected_voice = voice or self.voice_name
            selected_rate = rate or self.speaking_rate
            
            self.logger.info(f"🗣️ Speaking: '{text}' (voice: {selected_voice})")
            
            # Команда для синтеза речи
            cmd = ['say', '-v', selected_voice, '-r', str(selected_rate), text]
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                self.logger.debug("✅ Speech synthesis completed")
                return CommandResult(True, "Speech completed successfully")
            else:
                error_msg = stderr.decode('utf-8') if stderr else "Unknown error"
                self.logger.error(f"❌ Speech synthesis failed: {error_msg}")
                return CommandResult(False, f"Speech failed: {error_msg}")
                
        except Exception as e:
            self.logger.error(f"Error in speech synthesis: {e}")
            return CommandResult(False, f"Speech error: {e}")
        finally:
            self.is_speaking = False
    
    async def speak_async(self, text: str, voice: Optional[str] = None, rate: Optional[int] = None):
        """Произнести текст асинхронно (не ждать завершения)"""
        asyncio.create_task(self.speak(text, voice, rate))
    
    def stop_speaking(self):
        """Остановить текущую речь"""
        try:
            subprocess.run(['killall', 'say'], check=False)
            self.is_speaking = False
            self.logger.info("🔇 Stopped speaking")
        except Exception as e:
            self.logger.error(f"Error stopping speech: {e}")
    
    async def execute(self, command: str, **kwargs) -> CommandResult:
        """Выполнение команды модулем"""
        try:
            if command == "speak":
                text = kwargs.get("text", "")
                voice = kwargs.get("voice")
                rate = kwargs.get("rate")
                return await self.speak(text, voice, rate)
            elif command == "stop":
                self.stop_speaking()
                return CommandResult(True, "Speech stopped")
            else:
                return CommandResult(False, f"Unknown command: {command}")
                
        except Exception as e:
            return CommandResult(False, f"Error executing command: {e}", error=str(e))
    
    def can_handle(self, command: str) -> bool:
        """Проверка, может ли модуль обработать команду"""
        tts_commands = ["speak", "say", "voice", "talk", "stop"]
        return any(cmd in command.lower() for cmd in tts_commands)
    
    async def cleanup(self):
        """Очистка ресурсов модуля"""
        self.stop_speaking()
        await super().cleanup()
