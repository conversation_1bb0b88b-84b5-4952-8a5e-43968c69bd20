"""
Локальный процессор команд для Jarvis
"""

import re
from typing import Dict, List, Tuple, Optional
from jarvis.core.base import BaseModule, CommandResult
from jarvis.core.logger import jarvis_logger

class LocalCommandProcessor(BaseModule):
    """Локальный процессор команд без зависимости от внешних API"""
    
    def __init__(self):
        super().__init__("LocalCommandProcessor")
        self.command_patterns = {}
        self.intent_keywords = {}
        self._setup_patterns()
    
    def _setup_patterns(self):
        """Настройка паттернов распознавания команд"""
        
        # Паттерны для приложений
        self.command_patterns["app_open"] = [
            r"открой\s+(.+)",
            r"запусти\s+(.+)",
            r"включи\s+(.+)",
            r"open\s+(.+)",
            r"launch\s+(.+)",
            r"start\s+(.+)"
        ]
        
        self.command_patterns["app_close"] = [
            r"закрой\s+(.+)",
            r"выключи\s+(.+)",
            r"завершить?\s+(.+)",
            r"close\s+(.+)",
            r"quit\s+(.+)",
            r"exit\s+(.+)"
        ]
        
        # Паттерны для сообщений
        self.command_patterns["message_send"] = [
            r"напиши\s+(.+?)\s+сообщение\s+(.+)",
            r"отправь\s+(.+?)\s+сообщение\s+(.+)",
            r"пошли\s+(.+?)\s+(.+)",
            r"send\s+(.+?)\s+message\s+(.+)",
            r"write\s+to\s+(.+?)\s+(.+)"
        ]
        
        # Паттерны для системных команд
        self.command_patterns["system_volume"] = [
            r"громкость\s+(\d+)",
            r"звук\s+(\d+)",
            r"volume\s+(\d+)",
            r"увеличь\s+громкость",
            r"уменьши\s+громкость",
            r"volume\s+up",
            r"volume\s+down"
        ]
        
        self.command_patterns["system_brightness"] = [
            r"яркость\s+(\d+)",
            r"brightness\s+(\d+)",
            r"увеличь\s+яркость",
            r"уменьши\s+яркость",
            r"brightness\s+up",
            r"brightness\s+down"
        ]
        
        # Паттерны для поиска
        self.command_patterns["search_web"] = [
            r"найди\s+в\s+интернете\s+(.+)",
            r"поищи\s+(.+)",
            r"гугли\s+(.+)",
            r"search\s+(.+)",
            r"google\s+(.+)"
        ]
        
        self.command_patterns["search_file"] = [
            r"найди\s+файл\s+(.+)",
            r"поищи\s+файл\s+(.+)",
            r"find\s+file\s+(.+)",
            r"locate\s+(.+)"
        ]
        
        # Ключевые слова для определения намерений
        self.intent_keywords = {
            "greeting": ["привет", "hello", "hi", "здравствуй", "добро пожаловать"],
            "goodbye": ["пока", "до свидания", "bye", "goodbye", "увидимся"],
            "thanks": ["спасибо", "благодарю", "thanks", "thank you"],
            "help": ["помощь", "help", "что ты умеешь", "команды"],
            "test": ["тест", "test", "проверка", "check"],
            "stop": ["стоп", "stop", "остановись", "хватит", "выход", "exit"]
        }
    
    async def initialize(self) -> bool:
        """Инициализация процессора команд"""
        self.logger.info("Initializing Local Command Processor...")
        self.is_initialized = True
        self.logger.info("Local Command Processor initialized successfully")
        return True
    
    def parse_command(self, text: str) -> Dict:
        """Парсинг команды и определение намерения"""
        text = text.lower().strip()
        
        # Проверяем простые намерения по ключевым словам
        for intent, keywords in self.intent_keywords.items():
            if any(keyword in text for keyword in keywords):
                return {
                    "intent": intent,
                    "confidence": 0.9,
                    "original_text": text,
                    "entities": []
                }
        
        # Проверяем сложные паттерны команд
        for command_type, patterns in self.command_patterns.items():
            for pattern in patterns:
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    entities = list(match.groups()) if match.groups() else []
                    return {
                        "intent": command_type,
                        "confidence": 0.8,
                        "original_text": text,
                        "entities": entities
                    }
        
        # Если ничего не найдено
        return {
            "intent": "unknown",
            "confidence": 0.1,
            "original_text": text,
            "entities": []
        }
    
    def extract_app_name(self, text: str) -> str:
        """Извлечение и нормализация названия приложения"""
        # Словарь для нормализации названий приложений
        app_aliases = {
            "телеграм": "Telegram",
            "телега": "Telegram", 
            "telegram": "Telegram",
            "сафари": "Safari",
            "safari": "Safari",
            "хром": "Google Chrome",
            "chrome": "Google Chrome",
            "гугл хром": "Google Chrome",
            "файндер": "Finder",
            "finder": "Finder",
            "терминал": "Terminal",
            "terminal": "Terminal",
            "калькулятор": "Калькулятор",
            "calculator": "Калькулятор",
            "заметки": "Notes",
            "notes": "Notes",
            "почта": "Mail",
            "mail": "Mail",
            "календарь": "Calendar",
            "calendar": "Calendar",
            "музыка": "Music",
            "music": "Music",
            "фото": "Photos",
            "photos": "Photos"
        }
        
        text = text.lower().strip()
        return app_aliases.get(text, text.title())
    
    async def execute(self, command: str, **kwargs) -> CommandResult:
        """Выполнение команды процессором"""
        try:
            parsed = self.parse_command(command)
            return CommandResult(
                True, 
                f"Command parsed: {parsed['intent']}", 
                data=parsed
            )
        except Exception as e:
            return CommandResult(False, f"Error parsing command: {e}", error=str(e))
    
    def can_handle(self, command: str) -> bool:
        """Процессор может обработать любую команду"""
        return True
    
    async def cleanup(self):
        """Очистка ресурсов"""
        await super().cleanup()
