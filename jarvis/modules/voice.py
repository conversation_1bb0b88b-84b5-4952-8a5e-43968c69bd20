"""
Модуль распознавания голоса для Jarvis
"""

import asyncio
import speech_recognition as sr
import threading
import queue
from typing import Optional, Callable
from jarvis.core.base import BaseModule, CommandResult
from jarvis.config import config

class VoiceModule(BaseModule):
    """Модуль для распознавания голоса и обработки голосовых команд"""
    
    def __init__(self):
        super().__init__("VoiceModule")
        self.recognizer = sr.Recognizer()
        self.microphone = None

        self.is_listening = False
        self.wake_word_detected = False
        self.command_callback: Optional[Callable] = None
        self.command_queue = queue.Queue()
        
    async def initialize(self) -> bool:
        """Инициализация модуля распознавания голоса"""
        try:
            self.logger.info("Initializing Voice Module...")
            self.logger.info("🔄 Using fully local speech recognition")

            # Инициализируем микрофон
            self.microphone = sr.Microphone()

            # Калибруем микрофон для шума окружения
            with self.microphone as source:
                self.logger.info("Calibrating microphone for ambient noise...")
                self.recognizer.adjust_for_ambient_noise(source, duration=1)

            self.logger.info("Voice Module initialized successfully")
            self.is_initialized = True
            return True

        except Exception as e:
            self.logger.error(f"Failed to initialize Voice Module: {e}")
            return False
    

    
    def set_command_callback(self, callback: Callable):
        """Установка callback функции для обработки команд"""
        self.command_callback = callback
    
    async def start_listening(self):
        """Запуск прослушивания голосовых команд"""
        if self.is_listening:
            return
            
        self.is_listening = True
        self.logger.info(f"🎤 Started listening for wake word: '{config.wake_word}'")
        
        # Запускаем прослушивание в отдельном потоке
        listen_thread = threading.Thread(target=self._listen_loop, daemon=True)
        listen_thread.start()
    
    def stop_listening(self):
        """Остановка прослушивания"""
        self.is_listening = False
        self.logger.info("🔇 Stopped listening")
    
    def _listen_loop(self):
        """Основной цикл прослушивания"""
        while self.is_listening:
            try:
                with self.microphone as source:
                    # Слушаем аудио
                    audio = self.recognizer.listen(
                        source, 
                        timeout=config.voice_recognition_timeout,
                        phrase_time_limit=config.voice_recognition_phrase_timeout
                    )
                
                # Распознаем речь
                text = self._recognize_speech(audio)
                if text:
                    self._process_voice_input_sync(text)
                    
            except sr.WaitTimeoutError:
                # Таймаут - это нормально, продолжаем слушать
                continue
            except Exception as e:
                self.logger.error(f"Error in listen loop: {e}")
                continue
    
    def _recognize_speech(self, audio) -> Optional[str]:
        """Распознавание речи из аудио"""
        try:
            # Используем Google Speech Recognition (бесплатный)
            text = self.recognizer.recognize_google(audio, language=config.language)
            self.logger.debug(f"Recognized speech: {text}")
            return text.lower()
        except sr.UnknownValueError:
            # Речь не распознана - это нормально
            return None
        except sr.RequestError as e:
            self.logger.error(f"Speech recognition error: {e}")
            return None

    def _process_voice_input_sync(self, text: str):
        """Синхронная обработка распознанного текста"""
        if not self.wake_word_detected:
            # Проверяем wake word
            if config.wake_word in text:
                self.wake_word_detected = True
                self.logger.info(f"🎯 Wake word detected! Listening for command...")
                return
        else:
            # Wake word уже был, добавляем команду в очередь
            self.logger.info(f"📝 Command received: {text}")
            self.command_queue.put(text)
            # Сбрасываем состояние wake word
            self.wake_word_detected = False

    async def _process_voice_input(self, text: str):
        """Обработка распознанного текста"""
        if not self.wake_word_detected:
            # Проверяем wake word
            if config.wake_word in text:
                self.wake_word_detected = True
                self.logger.info(f"🎯 Wake word detected! Listening for command...")
                # TODO: Добавить звуковой сигнал активации
                return
        else:
            # Wake word уже был, обрабатываем команду
            self.logger.info(f"📝 Command received: {text}")
            
            if self.command_callback:
                await self.command_callback(text)
            
            # Сбрасываем состояние wake word
            self.wake_word_detected = False

    def get_command(self) -> Optional[str]:
        """Получить команду из очереди (неблокирующий вызов)"""
        try:
            return self.command_queue.get_nowait()
        except queue.Empty:
            return None

    async def execute(self, command: str, **kwargs) -> CommandResult:
        """Выполнение команды модулем"""
        try:
            if command == "start_listening":
                await self.start_listening()
                return CommandResult(True, "Voice listening started")
            elif command == "stop_listening":
                self.stop_listening()
                return CommandResult(True, "Voice listening stopped")
            else:
                return CommandResult(False, f"Unknown command: {command}")
                
        except Exception as e:
            return CommandResult(False, f"Error executing command: {e}", error=str(e))
    
    def can_handle(self, command: str) -> bool:
        """Проверка, может ли модуль обработать команду"""
        voice_commands = ["start_listening", "stop_listening", "listen", "voice"]
        return any(cmd in command.lower() for cmd in voice_commands)
    
    async def cleanup(self):
        """Очистка ресурсов модуля"""
        self.stop_listening()
        await super().cleanup()
