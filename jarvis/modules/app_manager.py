"""
Модуль управления приложениями macOS для Jarvis
"""

import subprocess
import asyncio
import psutil
from typing import List, Dict, Optional
from jarvis.core.base import BaseModule, CommandResult

class AppManager(BaseModule):
    """Модуль для управления приложениями macOS"""
    
    def __init__(self):
        super().__init__("AppManager")
        self.running_apps = {}
    
    async def initialize(self) -> bool:
        """Инициализация модуля управления приложениями"""
        try:
            self.logger.info("Initializing App Manager...")
            
            # Проверяем доступность AppleScript
            result = await self._run_applescript('tell application "System Events" to get name of processes')
            if result:
                self.logger.info("✅ AppleScript is available")
            else:
                self.logger.warning("⚠️ AppleScript may not be available")
            
            await self._update_running_apps()
            self.is_initialized = True
            self.logger.info("App Manager initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize App Manager: {e}")
            return False
    
    async def _run_applescript(self, script: str) -> Optional[str]:
        """Выполнение AppleScript команды"""
        try:
            process = await asyncio.create_subprocess_exec(
                'osascript', '-e', script,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                return stdout.decode('utf-8').strip()
            else:
                self.logger.error(f"AppleScript error: {stderr.decode('utf-8')}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error running AppleScript: {e}")
            return None
    
    async def _update_running_apps(self):
        """Обновление списка запущенных приложений"""
        try:
            script = '''
            tell application "System Events"
                set appList to {}
                repeat with proc in processes
                    if background only of proc is false then
                        set end of appList to name of proc
                    end if
                end repeat
                return appList
            end tell
            '''
            
            result = await self._run_applescript(script)
            if result:
                apps = [app.strip() for app in result.split(', ')]
                self.running_apps = {app.lower(): app for app in apps}
                self.logger.debug(f"Found {len(self.running_apps)} running apps")
            
        except Exception as e:
            self.logger.error(f"Error updating running apps: {e}")
    
    async def find_application(self, app_name: str) -> Optional[str]:
        """Поиск приложения в системе"""
        try:
            # Сначала ищем в запущенных приложениях
            app_lower = app_name.lower()
            if app_lower in self.running_apps:
                return self.running_apps[app_lower]
            
            # Ищем частичные совпадения в запущенных приложениях
            for running_app_lower, running_app in self.running_apps.items():
                if app_lower in running_app_lower or running_app_lower in app_lower:
                    return running_app
            
            # Ищем в Applications через Spotlight (пробуем английское название)
            english_names = {
                "Калькулятор": "Calculator",
                "Календарь": "Calendar",
                "Заметки": "Notes",
                "Почта": "Mail",
                "Музыка": "Music",
                "Фото": "Photos"
            }

            search_names = [app_name]
            if app_name in english_names:
                search_names.append(english_names[app_name])

            for search_name in search_names:
                script = f'''
                tell application "System Events"
                    try
                        set appPath to path to application "{search_name}"
                        return name of application file appPath
                    on error
                        return ""
                    end try
                end tell
                '''

                result = await self._run_applescript(script)
                if result and result != "":
                    return result
            
            # Поиск через mdfind (Spotlight)
            try:
                process = await asyncio.create_subprocess_exec(
                    'mdfind', f'kMDItemKind == "Application" && kMDItemDisplayName == "*{app_name}*"',
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                stdout, stderr = await process.communicate()

                if process.returncode == 0 and stdout:
                    apps = stdout.decode('utf-8').strip().split('\n')
                    if apps and apps[0]:
                        # Извлекаем имя приложения из пути
                        app_path = apps[0]
                        app_name_from_path = app_path.split('/')[-1].replace('.app', '')
                        return app_name_from_path

            except Exception as e:
                self.logger.debug(f"mdfind search failed: {e}")

            # Последняя попытка - просто вернуть исходное имя для команды open
            return app_name
            
        except Exception as e:
            self.logger.error(f"Error finding application {app_name}: {e}")
            return None
    
    async def open_application(self, app_name: str) -> CommandResult:
        """Открытие приложения"""
        try:
            self.logger.info(f"🚀 Opening application: {app_name}")
            
            # Ищем приложение
            found_app = await self.find_application(app_name)
            if not found_app:
                return CommandResult(
                    False, 
                    f"Application '{app_name}' not found",
                    error="app_not_found"
                )
            
            # Открываем приложение (пробуем разные способы)
            methods = [
                f'tell application "{found_app}" to activate',
                f'do shell script "open -a \\"{found_app}\\""'
            ]

            result = None
            for method in methods:
                result = await self._run_applescript(method)
                if result is not None:
                    break
            
            if result is not None:
                await self._update_running_apps()
                self.logger.info(f"✅ Successfully opened {found_app}")
                return CommandResult(
                    True, 
                    f"Successfully opened {found_app}",
                    data={"app_name": found_app}
                )
            else:
                return CommandResult(
                    False,
                    f"Failed to open {found_app}",
                    error="applescript_failed"
                )
                
        except Exception as e:
            self.logger.error(f"Error opening application {app_name}: {e}")
            return CommandResult(False, f"Error opening application: {e}", error=str(e))
    
    async def close_application(self, app_name: str) -> CommandResult:
        """Закрытие приложения"""
        try:
            self.logger.info(f"🔴 Closing application: {app_name}")
            
            # Ищем приложение
            found_app = await self.find_application(app_name)
            if not found_app:
                return CommandResult(
                    False,
                    f"Application '{app_name}' not found or not running",
                    error="app_not_found"
                )
            
            # Закрываем приложение
            script = f'tell application "{found_app}" to quit'
            result = await self._run_applescript(script)
            
            if result is not None:
                await self._update_running_apps()
                self.logger.info(f"✅ Successfully closed {found_app}")
                return CommandResult(
                    True,
                    f"Successfully closed {found_app}",
                    data={"app_name": found_app}
                )
            else:
                return CommandResult(
                    False,
                    f"Failed to close {found_app}",
                    error="applescript_failed"
                )
                
        except Exception as e:
            self.logger.error(f"Error closing application {app_name}: {e}")
            return CommandResult(False, f"Error closing application: {e}", error=str(e))
    
    async def list_running_applications(self) -> CommandResult:
        """Получение списка запущенных приложений"""
        try:
            await self._update_running_apps()
            apps_list = list(self.running_apps.values())
            
            self.logger.info(f"📱 Found {len(apps_list)} running applications")
            return CommandResult(
                True,
                f"Found {len(apps_list)} running applications",
                data={"apps": apps_list}
            )
            
        except Exception as e:
            self.logger.error(f"Error listing applications: {e}")
            return CommandResult(False, f"Error listing applications: {e}", error=str(e))
    
    async def execute(self, command: str, **kwargs) -> CommandResult:
        """Выполнение команды модулем"""
        try:
            if command == "open":
                app_name = kwargs.get("app_name", "")
                return await self.open_application(app_name)
            elif command == "close":
                app_name = kwargs.get("app_name", "")
                return await self.close_application(app_name)
            elif command == "list":
                return await self.list_running_applications()
            else:
                return CommandResult(False, f"Unknown command: {command}")
                
        except Exception as e:
            return CommandResult(False, f"Error executing command: {e}", error=str(e))
    
    def can_handle(self, command: str) -> bool:
        """Проверка, может ли модуль обработать команду"""
        app_commands = ["open", "close", "launch", "quit", "start", "stop", "app", "application"]
        return any(cmd in command.lower() for cmd in app_commands)
    
    async def cleanup(self):
        """Очистка ресурсов модуля"""
        await super().cleanup()
