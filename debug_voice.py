#!/usr/bin/env python3
"""
Отладка голосового модуля
"""

import asyncio
import time
from jarvis.modules.voice import VoiceModule
from jarvis.core.logger import jarvis_logger

async def debug_voice_recognition():
    """Отладка распознавания голоса"""
    jarvis_logger.info("🔍 Starting Voice Recognition Debug...")
    
    # Создаем голосовой модуль
    voice_module = VoiceModule()
    
    # Инициализируем
    success = await voice_module.initialize()
    if not success:
        jarvis_logger.error("❌ Failed to initialize voice module")
        return
    
    jarvis_logger.info("✅ Voice module initialized")
    jarvis_logger.info("🎤 Starting to listen...")
    jarvis_logger.info("📢 Say 'jarvis' followed by any command")
    jarvis_logger.info("🔍 Debug mode - will show all recognized speech")
    
    # Запускаем прослушивание
    await voice_module.start_listening()
    
    # Мониторим очередь команд
    last_queue_check = time.time()
    command_count = 0
    
    try:
        while True:
            # Проверяем очередь каждые 0.1 секунды
            command = voice_module.get_command()
            if command:
                command_count += 1
                jarvis_logger.info(f"🎯 COMMAND #{command_count} RECEIVED: '{command}'")
                jarvis_logger.info(f"📝 Processing command...")
                
                # Простая обработка для отладки
                if "привет" in command.lower():
                    jarvis_logger.info("👋 Detected greeting!")
                elif "тест" in command.lower():
                    jarvis_logger.info("🧪 Detected test command!")
                elif "открой" in command.lower():
                    jarvis_logger.info("🚀 Detected open command!")
                else:
                    jarvis_logger.info(f"🤔 Unknown command: {command}")
            
            # Показываем статус каждые 10 секунд
            current_time = time.time()
            if current_time - last_queue_check > 10:
                jarvis_logger.info(f"📊 Status: Listening... Commands received: {command_count}")
                jarvis_logger.info(f"🔊 Wake word detected: {voice_module.wake_word_detected}")
                jarvis_logger.info(f"🎤 Is listening: {voice_module.is_listening}")
                last_queue_check = current_time
            
            await asyncio.sleep(0.1)
            
    except KeyboardInterrupt:
        jarvis_logger.info("🛑 Debug interrupted by user")
    finally:
        await voice_module.cleanup()
        jarvis_logger.info(f"🏁 Debug completed. Total commands received: {command_count}")

async def main():
    """Главная функция отладки"""
    try:
        await debug_voice_recognition()
    except Exception as e:
        jarvis_logger.error(f"💥 Debug failed with error: {e}")

if __name__ == "__main__":
    asyncio.run(main())
