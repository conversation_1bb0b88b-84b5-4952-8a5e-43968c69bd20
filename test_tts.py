#!/usr/bin/env python3
"""
Тест модуля синтеза речи (TTS)
"""

import asyncio
from jarvis.modules.tts import TTSModule
from jarvis.core.logger import jarvis_logger

async def test_tts():
    """Тестирование модуля TTS"""
    jarvis_logger.info("🚀 Starting TTS Module test...")
    
    # Инициализируем модуль
    tts_module = TTSModule()
    
    success = await tts_module.initialize()
    if not success:
        jarvis_logger.error("❌ Failed to initialize TTS Module")
        return False
    
    jarvis_logger.info("✅ TTS Module initialized successfully!")
    
    # Тестовые фразы
    test_phrases = [
        "Привет! Я ваш персональный помощник Джарвис!",
        "Тестирую синтез речи",
        "Все системы работают нормально",
        "Открываю Сафари",
        "До свидания!"
    ]
    
    for i, phrase in enumerate(test_phrases, 1):
        jarvis_logger.info(f"\n--- Test {i}: Speaking '{phrase}' ---")
        
        result = await tts_module.speak(phrase)
        if result.success:
            jarvis_logger.info(f"✅ {result.message}")
        else:
            jarvis_logger.error(f"❌ {result.message}")
        
        # Небольшая пауза между фразами
        await asyncio.sleep(1)
    
    # Тест разных голосов
    jarvis_logger.info("\n--- Testing different voices ---")
    
    voices_to_test = ["Milena", "Yuri", "Alex"]
    test_text = "Тестирую разные голоса"
    
    for voice in voices_to_test:
        jarvis_logger.info(f"Testing voice: {voice}")
        result = await tts_module.speak(test_text, voice=voice)
        if result.success:
            jarvis_logger.info(f"✅ Voice {voice} works")
        else:
            jarvis_logger.error(f"❌ Voice {voice} failed: {result.message}")
        await asyncio.sleep(1)
    
    # Очистка
    await tts_module.cleanup()
    
    jarvis_logger.info("\n🏁 TTS Module test completed!")
    return True

async def main():
    """Главная функция теста"""
    try:
        await test_tts()
    except KeyboardInterrupt:
        jarvis_logger.info("🛑 Test interrupted by user")
    except Exception as e:
        jarvis_logger.error(f"💥 Test failed with error: {e}")

if __name__ == "__main__":
    asyncio.run(main())
