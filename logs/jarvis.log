2025-07-12 10:53:53 | INFO     | __main__:initialize:23 | Initializing Jarvis AI Assistant...
2025-07-12 10:53:53 | INFO     | __main__:initialize:47 | Jarvis initialization complete!
2025-07-12 10:53:53 | INFO     | __main__:start:54 | <PERSON> is now listening...
2025-07-12 10:53:53 | INFO     | __main__:start:55 | Say 'jarvis' to activate
2025-07-12 10:54:08 | INFO     | __main__:signal_handler:87 | Received signal 2
2025-07-12 10:54:08 | INFO     | __main__:shutdown:69 | Shutting down Jarvis...
2025-07-12 10:54:08 | INFO     | __main__:shutdown:79 | <PERSON> shutdown complete
2025-07-12 10:57:08 | INFO     | __main__:main:45 | 🚀 Starting OpenAI API test...
2025-07-12 10:57:08 | INFO     | __main__:test_openai_api:14 | 🔑 Testing OpenAI API key...
2025-07-12 10:57:08 | INFO     | __main__:test_openai_api:15 | API Key: sk-ijklqrst5678uvwxi...
2025-07-12 10:57:09 | ERROR    | __main__:test_openai_api:40 | ❌ OpenAI API test failed: Error code: 401 - {'error': {'message': 'Incorrect API key provided: sk-ijklq*******************************qrst. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-07-12 10:57:09 | ERROR    | __main__:main:52 | 💥 API test failed!
2025-07-12 10:58:24 | INFO     | __main__:initialize:24 | Initializing Jarvis AI Assistant...
2025-07-12 10:58:24 | INFO     | jarvis.modules.voice:initialize:28 | Initializing Voice Module...
2025-07-12 10:58:24 | INFO     | jarvis.modules.voice:_test_openai_connection:60 | Testing OpenAI API connection...
2025-07-12 10:58:27 | ERROR    | jarvis.modules.voice:_test_openai_connection:77 | ❌ OpenAI API connection failed: Error code: 401 - {'error': {'message': 'Incorrect API key provided: sk-ijklq*******************************qrst. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-07-12 10:58:27 | WARNING  | jarvis.modules.voice:initialize:37 | OpenAI API not available: Error code: 401 - {'error': {'message': 'Incorrect API key provided: sk-ijklq*******************************qrst. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-07-12 10:58:27 | INFO     | jarvis.modules.voice:initialize:38 | 🔄 Continuing with local speech recognition only
2025-07-12 10:58:29 | INFO     | jarvis.modules.voice:initialize:47 | Calibrating microphone for ambient noise...
2025-07-12 10:58:30 | INFO     | jarvis.modules.voice:initialize:50 | Voice Module initialized successfully
2025-07-12 10:58:30 | INFO     | __main__:initialize:46 | Module VoiceModule initialized successfully
2025-07-12 10:58:30 | INFO     | __main__:initialize:52 | Jarvis initialization complete!
2025-07-12 10:58:30 | INFO     | __main__:start:59 | Jarvis is now listening...
2025-07-12 10:58:30 | INFO     | __main__:start:60 | Say 'jarvis' to activate
2025-07-12 10:58:49 | INFO     | __main__:signal_handler:111 | Received signal 2
2025-07-12 10:58:49 | INFO     | __main__:shutdown:77 | Shutting down Jarvis...
2025-07-12 10:58:49 | INFO     | jarvis.modules.voice:stop_listening:99 | 🔇 Stopped listening
2025-07-12 10:58:49 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: VoiceModule
2025-07-12 10:58:49 | INFO     | __main__:shutdown:87 | Jarvis shutdown complete
2025-07-12 10:59:43 | INFO     | __main__:initialize:24 | Initializing Jarvis AI Assistant...
2025-07-12 10:59:43 | INFO     | jarvis.modules.voice:initialize:28 | Initializing Voice Module...
2025-07-12 10:59:43 | INFO     | jarvis.modules.voice:_test_openai_connection:61 | Testing OpenAI API connection...
2025-07-12 10:59:44 | ERROR    | jarvis.modules.voice:_test_openai_connection:78 | ❌ OpenAI API connection failed: Error code: 401 - {'error': {'message': 'Incorrect API key provided: sk-ijklq*******************************qrst. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-07-12 10:59:44 | WARNING  | jarvis.modules.voice:initialize:37 | OpenAI API not available: Error code: 401 - {'error': {'message': 'Incorrect API key provided: sk-ijklq*******************************qrst. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-07-12 10:59:44 | INFO     | jarvis.modules.voice:initialize:38 | 🔄 Continuing with local speech recognition only
2025-07-12 10:59:44 | INFO     | jarvis.modules.voice:initialize:47 | Calibrating microphone for ambient noise...
2025-07-12 10:59:45 | INFO     | jarvis.modules.voice:initialize:50 | Voice Module initialized successfully
2025-07-12 10:59:45 | INFO     | __main__:initialize:46 | Module VoiceModule initialized successfully
2025-07-12 10:59:45 | INFO     | __main__:initialize:52 | Jarvis initialization complete!
2025-07-12 10:59:45 | INFO     | __main__:start:59 | Jarvis is now listening...
2025-07-12 10:59:45 | INFO     | __main__:start:60 | Say 'jarvis' to activate
2025-07-12 10:59:45 | INFO     | jarvis.modules.voice:start_listening:91 | 🎤 Started listening for wake word: 'jarvis'
2025-07-12 11:00:00 | ERROR    | jarvis.modules.voice:_listen_loop:126 | Error in listen loop: There is no current event loop in thread 'Thread-1'.
2025-07-12 11:00:09 | ERROR    | jarvis.modules.voice:_listen_loop:126 | Error in listen loop: There is no current event loop in thread 'Thread-1'.
2025-07-12 11:00:15 | ERROR    | jarvis.modules.voice:_listen_loop:126 | Error in listen loop: There is no current event loop in thread 'Thread-1'.
2025-07-12 11:00:35 | INFO     | __main__:signal_handler:111 | Received signal 2
2025-07-12 11:00:35 | INFO     | __main__:shutdown:77 | Shutting down Jarvis...
2025-07-12 11:00:35 | INFO     | jarvis.modules.voice:stop_listening:100 | 🔇 Stopped listening
2025-07-12 11:00:35 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: VoiceModule
2025-07-12 11:00:35 | INFO     | __main__:shutdown:87 | Jarvis shutdown complete
2025-07-12 11:05:10 | INFO     | __main__:test_app_manager:13 | 🚀 Starting App Manager test...
2025-07-12 11:05:10 | INFO     | jarvis.modules.app_manager:initialize:21 | Initializing App Manager...
2025-07-12 11:05:12 | INFO     | jarvis.modules.app_manager:initialize:26 | ✅ AppleScript is available
2025-07-12 11:05:13 | INFO     | jarvis.modules.app_manager:initialize:32 | App Manager initialized successfully
2025-07-12 11:05:13 | INFO     | jarvis.modules.command_processor:initialize:98 | Initializing Local Command Processor...
2025-07-12 11:05:13 | INFO     | jarvis.modules.command_processor:initialize:100 | Local Command Processor initialized successfully
2025-07-12 11:05:13 | INFO     | __main__:test_app_manager:28 | ✅ Modules initialized successfully
2025-07-12 11:05:13 | INFO     | __main__:test_app_manager:31 | 
📱 Test 1: Listing running applications...
2025-07-12 11:05:13 | INFO     | jarvis.modules.app_manager:list_running_applications:218 | 📱 Found 5 running applications
2025-07-12 11:05:13 | INFO     | __main__:test_app_manager:35 | ✅ Found 5 running applications:
2025-07-12 11:05:13 | INFO     | __main__:test_app_manager:37 |    • Telegram
2025-07-12 11:05:13 | INFO     | __main__:test_app_manager:37 |    • Safari
2025-07-12 11:05:13 | INFO     | __main__:test_app_manager:37 |    • Terminal
2025-07-12 11:05:13 | INFO     | __main__:test_app_manager:37 |    • Finder
2025-07-12 11:05:13 | INFO     | __main__:test_app_manager:37 |    • Electron
2025-07-12 11:05:13 | INFO     | __main__:test_app_manager:44 | 
🧠 Test 2: Command parsing...
2025-07-12 11:05:13 | INFO     | __main__:test_app_manager:55 |    'открой калькулятор' -> app_open (entities: ['калькулятор'])
2025-07-12 11:05:13 | INFO     | __main__:test_app_manager:55 |    'запусти сафари' -> app_open (entities: ['сафари'])
2025-07-12 11:05:13 | INFO     | __main__:test_app_manager:55 |    'закрой терминал' -> app_close (entities: ['терминал'])
2025-07-12 11:05:13 | INFO     | __main__:test_app_manager:55 |    'open calculator' -> app_open (entities: ['calculator'])
2025-07-12 11:05:13 | INFO     | __main__:test_app_manager:55 |    'launch safari' -> app_open (entities: ['safari'])
2025-07-12 11:05:13 | INFO     | __main__:test_app_manager:58 | 
🔍 Test 3: Finding applications...
2025-07-12 11:05:13 | ERROR    | jarvis.modules.app_manager:_run_applescript:52 | AppleScript error: 186:193: syntax error: Предполагаемый результат — конец строки и т. д.; полученный — идентификатор. (-2741)

2025-07-12 11:05:13 | INFO     | __main__:test_app_manager:66 |    ❌ Not found: Calculator
2025-07-12 11:05:13 | INFO     | __main__:test_app_manager:64 |    ✅ Found: Safari -> Safari
2025-07-12 11:05:13 | INFO     | __main__:test_app_manager:64 |    ✅ Found: Finder -> Finder
2025-07-12 11:05:13 | INFO     | __main__:test_app_manager:64 |    ✅ Found: Terminal -> Terminal
2025-07-12 11:05:13 | INFO     | __main__:test_app_manager:69 | 
🚀 Test 4: Opening Calculator...
2025-07-12 11:05:13 | INFO     | jarvis.modules.app_manager:open_application:141 | 🚀 Opening application: Calculator
2025-07-12 11:05:13 | ERROR    | jarvis.modules.app_manager:_run_applescript:52 | AppleScript error: 186:193: syntax error: Предполагаемый результат — конец строки и т. д.; полученный — идентификатор. (-2741)

2025-07-12 11:05:13 | ERROR    | __main__:test_app_manager:83 | ❌ Application 'Calculator' not found
2025-07-12 11:05:13 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: AppManager
2025-07-12 11:05:13 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: LocalCommandProcessor
2025-07-12 11:05:13 | INFO     | __main__:test_app_manager:89 | 
🏁 App Manager test completed!
2025-07-12 11:05:34 | INFO     | __main__:initialize:26 | Initializing Jarvis AI Assistant...
2025-07-12 11:05:34 | INFO     | jarvis.modules.voice:initialize:27 | Initializing Voice Module...
2025-07-12 11:05:34 | INFO     | jarvis.modules.voice:initialize:28 | 🔄 Using fully local speech recognition
2025-07-12 11:05:34 | INFO     | jarvis.modules.voice:initialize:35 | Calibrating microphone for ambient noise...
2025-07-12 11:05:35 | INFO     | jarvis.modules.voice:initialize:38 | Voice Module initialized successfully
2025-07-12 11:05:35 | INFO     | __main__:initialize:51 | Module VoiceModule initialized successfully
2025-07-12 11:05:35 | INFO     | jarvis.modules.command_processor:initialize:98 | Initializing Local Command Processor...
2025-07-12 11:05:35 | INFO     | jarvis.modules.command_processor:initialize:100 | Local Command Processor initialized successfully
2025-07-12 11:05:35 | INFO     | __main__:initialize:51 | Module LocalCommandProcessor initialized successfully
2025-07-12 11:05:35 | INFO     | jarvis.modules.app_manager:initialize:21 | Initializing App Manager...
2025-07-12 11:05:35 | INFO     | jarvis.modules.app_manager:initialize:26 | ✅ AppleScript is available
2025-07-12 11:05:35 | INFO     | jarvis.modules.app_manager:initialize:32 | App Manager initialized successfully
2025-07-12 11:05:35 | INFO     | __main__:initialize:51 | Module AppManager initialized successfully
2025-07-12 11:05:35 | INFO     | __main__:initialize:57 | Jarvis initialization complete!
2025-07-12 11:05:35 | INFO     | __main__:start:64 | Jarvis is now listening...
2025-07-12 11:05:35 | INFO     | __main__:start:65 | Say 'jarvis' to activate
2025-07-12 11:05:35 | INFO     | jarvis.modules.voice:start_listening:58 | 🎤 Started listening for wake word: 'jarvis'
2025-07-12 11:05:54 | INFO     | __main__:signal_handler:173 | Received signal 2
2025-07-12 11:05:54 | INFO     | __main__:shutdown:82 | Shutting down Jarvis...
2025-07-12 11:05:54 | INFO     | jarvis.modules.voice:stop_listening:67 | 🔇 Stopped listening
2025-07-12 11:05:54 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: VoiceModule
2025-07-12 11:05:54 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: LocalCommandProcessor
2025-07-12 11:05:54 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: AppManager
2025-07-12 11:05:54 | INFO     | __main__:shutdown:92 | Jarvis shutdown complete
2025-07-12 11:06:50 | INFO     | __main__:initialize:26 | Initializing Jarvis AI Assistant...
2025-07-12 11:06:50 | INFO     | jarvis.modules.voice:initialize:27 | Initializing Voice Module...
2025-07-12 11:06:50 | INFO     | jarvis.modules.voice:initialize:28 | 🔄 Using fully local speech recognition
2025-07-12 11:06:50 | INFO     | jarvis.modules.voice:initialize:35 | Calibrating microphone for ambient noise...
2025-07-12 11:06:52 | INFO     | jarvis.modules.voice:initialize:38 | Voice Module initialized successfully
2025-07-12 11:06:52 | INFO     | __main__:initialize:51 | Module VoiceModule initialized successfully
2025-07-12 11:06:52 | INFO     | jarvis.modules.command_processor:initialize:98 | Initializing Local Command Processor...
2025-07-12 11:06:52 | INFO     | jarvis.modules.command_processor:initialize:100 | Local Command Processor initialized successfully
2025-07-12 11:06:52 | INFO     | __main__:initialize:51 | Module LocalCommandProcessor initialized successfully
2025-07-12 11:06:52 | INFO     | jarvis.modules.app_manager:initialize:21 | Initializing App Manager...
2025-07-12 11:06:52 | INFO     | jarvis.modules.app_manager:initialize:26 | ✅ AppleScript is available
2025-07-12 11:06:52 | INFO     | jarvis.modules.app_manager:initialize:32 | App Manager initialized successfully
2025-07-12 11:06:52 | INFO     | __main__:initialize:51 | Module AppManager initialized successfully
2025-07-12 11:06:52 | INFO     | __main__:initialize:57 | Jarvis initialization complete!
2025-07-12 11:06:52 | INFO     | __main__:start:64 | Jarvis is now listening...
2025-07-12 11:06:52 | INFO     | __main__:start:65 | Say 'jarvis' to activate
2025-07-12 11:06:52 | INFO     | jarvis.modules.voice:start_listening:58 | 🎤 Started listening for wake word: 'jarvis'
2025-07-12 11:06:58 | ERROR    | jarvis.modules.voice:_listen_loop:93 | Error in listen loop: There is no current event loop in thread 'Thread-1'.
2025-07-12 11:07:04 | ERROR    | jarvis.modules.voice:_listen_loop:93 | Error in listen loop: There is no current event loop in thread 'Thread-1'.
2025-07-12 11:07:08 | ERROR    | jarvis.modules.voice:_listen_loop:93 | Error in listen loop: There is no current event loop in thread 'Thread-1'.
2025-07-12 11:07:12 | ERROR    | jarvis.modules.voice:_listen_loop:93 | Error in listen loop: There is no current event loop in thread 'Thread-1'.
2025-07-12 11:07:16 | INFO     | __main__:signal_handler:173 | Received signal 2
2025-07-12 11:07:17 | INFO     | __main__:shutdown:82 | Shutting down Jarvis...
2025-07-12 11:07:17 | INFO     | jarvis.modules.voice:stop_listening:67 | 🔇 Stopped listening
2025-07-12 11:07:17 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: VoiceModule
2025-07-12 11:07:17 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: LocalCommandProcessor
2025-07-12 11:07:17 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: AppManager
2025-07-12 11:07:17 | INFO     | __main__:shutdown:92 | Jarvis shutdown complete
2025-07-12 11:09:51 | INFO     | __main__:initialize:26 | Initializing Jarvis AI Assistant...
2025-07-12 11:09:51 | INFO     | jarvis.modules.voice:initialize:29 | Initializing Voice Module...
2025-07-12 11:09:51 | INFO     | jarvis.modules.voice:initialize:30 | 🔄 Using fully local speech recognition
2025-07-12 11:09:51 | INFO     | jarvis.modules.voice:initialize:37 | Calibrating microphone for ambient noise...
2025-07-12 11:09:52 | INFO     | jarvis.modules.voice:initialize:40 | Voice Module initialized successfully
2025-07-12 11:09:52 | INFO     | __main__:initialize:51 | Module VoiceModule initialized successfully
2025-07-12 11:09:52 | INFO     | jarvis.modules.command_processor:initialize:98 | Initializing Local Command Processor...
2025-07-12 11:09:52 | INFO     | jarvis.modules.command_processor:initialize:100 | Local Command Processor initialized successfully
2025-07-12 11:09:52 | INFO     | __main__:initialize:51 | Module LocalCommandProcessor initialized successfully
2025-07-12 11:09:52 | INFO     | jarvis.modules.app_manager:initialize:21 | Initializing App Manager...
2025-07-12 11:09:53 | INFO     | jarvis.modules.app_manager:initialize:26 | ✅ AppleScript is available
2025-07-12 11:09:53 | INFO     | jarvis.modules.app_manager:initialize:32 | App Manager initialized successfully
2025-07-12 11:09:53 | INFO     | __main__:initialize:51 | Module AppManager initialized successfully
2025-07-12 11:09:53 | INFO     | __main__:initialize:57 | Jarvis initialization complete!
2025-07-12 11:09:53 | INFO     | __main__:start:64 | Jarvis is now listening...
2025-07-12 11:09:53 | INFO     | __main__:start:65 | Say 'jarvis' to activate
2025-07-12 11:09:53 | INFO     | jarvis.modules.voice:start_listening:60 | 🎤 Started listening for wake word: 'jarvis'
2025-07-12 11:10:24 | INFO     | __main__:signal_handler:179 | Received signal 2
2025-07-12 11:10:24 | INFO     | __main__:shutdown:88 | Shutting down Jarvis...
2025-07-12 11:10:24 | INFO     | jarvis.modules.voice:stop_listening:69 | 🔇 Stopped listening
2025-07-12 11:10:24 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: VoiceModule
2025-07-12 11:10:24 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: LocalCommandProcessor
2025-07-12 11:10:24 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: AppManager
2025-07-12 11:10:24 | INFO     | __main__:shutdown:98 | Jarvis shutdown complete
2025-07-12 11:10:36 | INFO     | __main__:simulate_voice_commands:14 | 🚀 Starting Voice Commands Simulation...
2025-07-12 11:10:36 | INFO     | jarvis.modules.command_processor:initialize:98 | Initializing Local Command Processor...
2025-07-12 11:10:36 | INFO     | jarvis.modules.command_processor:initialize:100 | Local Command Processor initialized successfully
2025-07-12 11:10:36 | INFO     | jarvis.modules.app_manager:initialize:21 | Initializing App Manager...
2025-07-12 11:10:36 | INFO     | jarvis.modules.app_manager:initialize:26 | ✅ AppleScript is available
2025-07-12 11:10:36 | INFO     | jarvis.modules.app_manager:initialize:32 | App Manager initialized successfully
2025-07-12 11:10:36 | INFO     | __main__:simulate_voice_commands:26 | ✅ Modules initialized
2025-07-12 11:10:36 | INFO     | __main__:simulate_voice_commands:87 | 
--- Test 1: 'привет' ---
2025-07-12 11:10:36 | INFO     | jarvis.modules.voice:_process_voice_input_sync:119 | 📝 Command received: привет
2025-07-12 11:10:36 | INFO     | __main__:process_command:39 | 🎤 Processing voice command: 'привет'
2025-07-12 11:10:36 | INFO     | __main__:process_command:52 | 🧠 Detected intent: greeting
2025-07-12 11:10:36 | INFO     | __main__:process_command:56 | 👋 Привет! Я ваш персональный помощник Jarvis!
2025-07-12 11:10:37 | INFO     | __main__:simulate_voice_commands:87 | 
--- Test 2: 'открой калькулятор' ---
2025-07-12 11:10:37 | INFO     | jarvis.modules.voice:_process_voice_input_sync:119 | 📝 Command received: открой калькулятор
2025-07-12 11:10:37 | INFO     | __main__:process_command:39 | 🎤 Processing voice command: 'открой калькулятор'
2025-07-12 11:10:37 | INFO     | __main__:process_command:52 | 🧠 Detected intent: app_open
2025-07-12 11:10:37 | INFO     | __main__:process_command:69 | 🚀 Opening application: Калькулятор
2025-07-12 11:10:37 | INFO     | jarvis.modules.app_manager:open_application:141 | 🚀 Opening application: Калькулятор
2025-07-12 11:10:37 | ERROR    | jarvis.modules.app_manager:_run_applescript:52 | AppleScript error: 187:194: syntax error: Предполагаемый результат — конец строки и т. д.; полученный — идентификатор. (-2741)

2025-07-12 11:10:37 | ERROR    | __main__:process_command:74 | ❌ Application 'Калькулятор' not found
2025-07-12 11:10:38 | INFO     | __main__:simulate_voice_commands:87 | 
--- Test 3: 'запусти сафари' ---
2025-07-12 11:10:38 | INFO     | jarvis.modules.voice:_process_voice_input_sync:119 | 📝 Command received: запусти сафари
2025-07-12 11:10:38 | INFO     | __main__:process_command:39 | 🎤 Processing voice command: 'запусти сафари'
2025-07-12 11:10:38 | INFO     | __main__:process_command:52 | 🧠 Detected intent: app_open
2025-07-12 11:10:38 | INFO     | __main__:process_command:69 | 🚀 Opening application: Safari
2025-07-12 11:10:38 | INFO     | jarvis.modules.app_manager:open_application:141 | 🚀 Opening application: Safari
2025-07-12 11:10:39 | INFO     | jarvis.modules.app_manager:open_application:158 | ✅ Successfully opened Safari
2025-07-12 11:10:39 | INFO     | __main__:process_command:72 | ✅ Successfully opened Safari
2025-07-12 11:10:40 | INFO     | __main__:simulate_voice_commands:87 | 
--- Test 4: 'помощь' ---
2025-07-12 11:10:40 | INFO     | jarvis.modules.voice:_process_voice_input_sync:119 | 📝 Command received: помощь
2025-07-12 11:10:40 | INFO     | __main__:process_command:39 | 🎤 Processing voice command: 'помощь'
2025-07-12 11:10:40 | INFO     | __main__:process_command:52 | 🧠 Detected intent: help
2025-07-12 11:10:40 | INFO     | __main__:process_command:62 | 💡 Доступные команды:
2025-07-12 11:10:40 | INFO     | __main__:process_command:63 |    • 'Привет' - поприветствовать
2025-07-12 11:10:40 | INFO     | __main__:process_command:64 |    • 'Открой [приложение]' - запустить приложение
2025-07-12 11:10:40 | INFO     | __main__:process_command:65 |    • 'Тест' - проверить работу
2025-07-12 11:10:41 | INFO     | __main__:simulate_voice_commands:87 | 
--- Test 5: 'тест' ---
2025-07-12 11:10:41 | INFO     | jarvis.modules.voice:_process_voice_input_sync:119 | 📝 Command received: тест
2025-07-12 11:10:41 | INFO     | __main__:process_command:39 | 🎤 Processing voice command: 'тест'
2025-07-12 11:10:41 | INFO     | __main__:process_command:52 | 🧠 Detected intent: test
2025-07-12 11:10:41 | INFO     | __main__:process_command:59 | ✅ Все системы работают нормально!
2025-07-12 11:10:42 | INFO     | jarvis.modules.voice:stop_listening:69 | 🔇 Stopped listening
2025-07-12 11:10:42 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: VoiceModule
2025-07-12 11:10:42 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: LocalCommandProcessor
2025-07-12 11:10:42 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: AppManager
2025-07-12 11:10:42 | INFO     | __main__:simulate_voice_commands:107 | 
🏁 Voice Commands Simulation completed!
2025-07-12 11:11:08 | INFO     | __main__:simulate_voice_commands:14 | 🚀 Starting Voice Commands Simulation...
2025-07-12 11:11:08 | INFO     | jarvis.modules.command_processor:initialize:98 | Initializing Local Command Processor...
2025-07-12 11:11:08 | INFO     | jarvis.modules.command_processor:initialize:100 | Local Command Processor initialized successfully
2025-07-12 11:11:08 | INFO     | jarvis.modules.app_manager:initialize:21 | Initializing App Manager...
2025-07-12 11:11:08 | INFO     | jarvis.modules.app_manager:initialize:26 | ✅ AppleScript is available
2025-07-12 11:11:08 | INFO     | jarvis.modules.app_manager:initialize:32 | App Manager initialized successfully
2025-07-12 11:11:08 | INFO     | __main__:simulate_voice_commands:26 | ✅ Modules initialized
2025-07-12 11:11:08 | INFO     | __main__:simulate_voice_commands:87 | 
--- Test 1: 'привет' ---
2025-07-12 11:11:08 | INFO     | jarvis.modules.voice:_process_voice_input_sync:119 | 📝 Command received: привет
2025-07-12 11:11:08 | INFO     | __main__:process_command:39 | 🎤 Processing voice command: 'привет'
2025-07-12 11:11:08 | INFO     | __main__:process_command:52 | 🧠 Detected intent: greeting
2025-07-12 11:11:08 | INFO     | __main__:process_command:56 | 👋 Привет! Я ваш персональный помощник Jarvis!
2025-07-12 11:11:09 | INFO     | __main__:simulate_voice_commands:87 | 
--- Test 2: 'открой калькулятор' ---
2025-07-12 11:11:09 | INFO     | jarvis.modules.voice:_process_voice_input_sync:119 | 📝 Command received: открой калькулятор
2025-07-12 11:11:09 | INFO     | __main__:process_command:39 | 🎤 Processing voice command: 'открой калькулятор'
2025-07-12 11:11:09 | INFO     | __main__:process_command:52 | 🧠 Detected intent: app_open
2025-07-12 11:11:09 | INFO     | __main__:process_command:69 | 🚀 Opening application: Калькулятор
2025-07-12 11:11:09 | INFO     | jarvis.modules.app_manager:open_application:155 | 🚀 Opening application: Калькулятор
2025-07-12 11:11:09 | ERROR    | jarvis.modules.app_manager:_run_applescript:52 | AppleScript error: 203:210: syntax error: Предполагаемый результат — конец строки и т. д.; полученный — идентификатор. (-2741)

2025-07-12 11:11:09 | ERROR    | jarvis.modules.app_manager:_run_applescript:52 | AppleScript error: 202:209: syntax error: Предполагаемый результат — конец строки и т. д.; полученный — идентификатор. (-2741)

2025-07-12 11:11:09 | ERROR    | __main__:process_command:74 | ❌ Application 'Калькулятор' not found
2025-07-12 11:11:10 | INFO     | __main__:simulate_voice_commands:87 | 
--- Test 3: 'запусти сафари' ---
2025-07-12 11:11:10 | INFO     | jarvis.modules.voice:_process_voice_input_sync:119 | 📝 Command received: запусти сафари
2025-07-12 11:11:10 | INFO     | __main__:process_command:39 | 🎤 Processing voice command: 'запусти сафари'
2025-07-12 11:11:10 | INFO     | __main__:process_command:52 | 🧠 Detected intent: app_open
2025-07-12 11:11:10 | INFO     | __main__:process_command:69 | 🚀 Opening application: Safari
2025-07-12 11:11:10 | INFO     | jarvis.modules.app_manager:open_application:155 | 🚀 Opening application: Safari
2025-07-12 11:11:11 | INFO     | jarvis.modules.app_manager:open_application:172 | ✅ Successfully opened Safari
2025-07-12 11:11:11 | INFO     | __main__:process_command:72 | ✅ Successfully opened Safari
2025-07-12 11:11:12 | INFO     | __main__:simulate_voice_commands:87 | 
--- Test 4: 'помощь' ---
2025-07-12 11:11:12 | INFO     | jarvis.modules.voice:_process_voice_input_sync:119 | 📝 Command received: помощь
2025-07-12 11:11:12 | INFO     | __main__:process_command:39 | 🎤 Processing voice command: 'помощь'
2025-07-12 11:11:12 | INFO     | __main__:process_command:52 | 🧠 Detected intent: help
2025-07-12 11:11:12 | INFO     | __main__:process_command:62 | 💡 Доступные команды:
2025-07-12 11:11:12 | INFO     | __main__:process_command:63 |    • 'Привет' - поприветствовать
2025-07-12 11:11:12 | INFO     | __main__:process_command:64 |    • 'Открой [приложение]' - запустить приложение
2025-07-12 11:11:12 | INFO     | __main__:process_command:65 |    • 'Тест' - проверить работу
2025-07-12 11:11:13 | INFO     | __main__:simulate_voice_commands:87 | 
--- Test 5: 'тест' ---
2025-07-12 11:11:13 | INFO     | jarvis.modules.voice:_process_voice_input_sync:119 | 📝 Command received: тест
2025-07-12 11:11:13 | INFO     | __main__:process_command:39 | 🎤 Processing voice command: 'тест'
2025-07-12 11:11:13 | INFO     | __main__:process_command:52 | 🧠 Detected intent: test
2025-07-12 11:11:13 | INFO     | __main__:process_command:59 | ✅ Все системы работают нормально!
2025-07-12 11:11:14 | INFO     | jarvis.modules.voice:stop_listening:69 | 🔇 Stopped listening
2025-07-12 11:11:14 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: VoiceModule
2025-07-12 11:11:14 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: LocalCommandProcessor
2025-07-12 11:11:14 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: AppManager
2025-07-12 11:11:14 | INFO     | __main__:simulate_voice_commands:107 | 
🏁 Voice Commands Simulation completed!
2025-07-12 11:12:00 | INFO     | __main__:initialize:26 | Initializing Jarvis AI Assistant...
2025-07-12 11:12:00 | INFO     | jarvis.modules.voice:initialize:29 | Initializing Voice Module...
2025-07-12 11:12:00 | INFO     | jarvis.modules.voice:initialize:30 | 🔄 Using fully local speech recognition
2025-07-12 11:12:00 | INFO     | jarvis.modules.voice:initialize:37 | Calibrating microphone for ambient noise...
2025-07-12 11:12:01 | INFO     | jarvis.modules.voice:initialize:40 | Voice Module initialized successfully
2025-07-12 11:12:01 | INFO     | __main__:initialize:51 | Module VoiceModule initialized successfully
2025-07-12 11:12:01 | INFO     | jarvis.modules.command_processor:initialize:98 | Initializing Local Command Processor...
2025-07-12 11:12:01 | INFO     | jarvis.modules.command_processor:initialize:100 | Local Command Processor initialized successfully
2025-07-12 11:12:01 | INFO     | __main__:initialize:51 | Module LocalCommandProcessor initialized successfully
2025-07-12 11:12:01 | INFO     | jarvis.modules.app_manager:initialize:21 | Initializing App Manager...
2025-07-12 11:12:01 | INFO     | jarvis.modules.app_manager:initialize:26 | ✅ AppleScript is available
2025-07-12 11:12:02 | INFO     | jarvis.modules.app_manager:initialize:32 | App Manager initialized successfully
2025-07-12 11:12:02 | INFO     | __main__:initialize:51 | Module AppManager initialized successfully
2025-07-12 11:12:02 | INFO     | __main__:initialize:57 | Jarvis initialization complete!
2025-07-12 11:12:02 | INFO     | __main__:start:64 | Jarvis is now listening...
2025-07-12 11:12:02 | INFO     | __main__:start:65 | Say 'jarvis' to activate
2025-07-12 11:12:02 | INFO     | jarvis.modules.voice:start_listening:60 | 🎤 Started listening for wake word: 'jarvis'
2025-07-12 11:12:23 | INFO     | __main__:signal_handler:179 | Received signal 2
2025-07-12 11:12:23 | INFO     | __main__:shutdown:88 | Shutting down Jarvis...
2025-07-12 11:12:23 | INFO     | jarvis.modules.voice:stop_listening:69 | 🔇 Stopped listening
2025-07-12 11:12:23 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: VoiceModule
2025-07-12 11:12:23 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: LocalCommandProcessor
2025-07-12 11:12:23 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: AppManager
2025-07-12 11:12:23 | INFO     | __main__:shutdown:98 | Jarvis shutdown complete
2025-07-12 11:15:41 | INFO     | __main__:test_tts:12 | 🚀 Starting TTS Module test...
2025-07-12 11:15:41 | INFO     | jarvis.modules.tts:initialize:23 | Initializing TTS Module...
2025-07-12 11:15:41 | ERROR    | jarvis.modules.tts:_test_say_command:56 | ❌ macOS 'say' command not available
2025-07-12 11:15:41 | WARNING  | jarvis.modules.tts:initialize:28 | ⚠️ TTS may not work properly
2025-07-12 11:15:41 | ERROR    | __main__:test_tts:19 | ❌ Failed to initialize TTS Module
2025-07-12 11:16:34 | INFO     | __main__:test_tts:12 | 🚀 Starting TTS Module test...
2025-07-12 11:16:34 | INFO     | jarvis.modules.tts:initialize:23 | Initializing TTS Module...
2025-07-12 11:16:34 | INFO     | jarvis.modules.tts:_test_say_command:54 | ✅ macOS 'say' command is available
2025-07-12 11:16:34 | INFO     | jarvis.modules.tts:_get_available_voices:87 | Available Russian voices: ['Milena']
2025-07-12 11:16:34 | INFO     | jarvis.modules.tts:_get_available_voices:88 | Available English voices: ['Albert', 'Bad', 'Bahh']...
2025-07-12 11:16:34 | INFO     | jarvis.modules.tts:_get_available_voices:100 | Selected voice: Milena
2025-07-12 11:16:34 | INFO     | jarvis.modules.tts:initialize:35 | ✅ TTS Module initialized successfully
2025-07-12 11:16:34 | INFO     | __main__:test_tts:22 | ✅ TTS Module initialized successfully!
2025-07-12 11:16:34 | INFO     | __main__:test_tts:34 | 
--- Test 1: Speaking 'Привет! Я ваш персональный помощник Джарвис!' ---
2025-07-12 11:16:34 | INFO     | jarvis.modules.tts:speak:116 | 🗣️ Speaking: 'Привет! Я ваш персональный помощник Джарвис!' (voice: Milena)
2025-07-12 11:16:37 | INFO     | __main__:test_tts:38 | ✅ Speech completed successfully
2025-07-12 11:16:38 | INFO     | __main__:test_tts:34 | 
--- Test 2: Speaking 'Тестирую синтез речи' ---
2025-07-12 11:16:38 | INFO     | jarvis.modules.tts:speak:116 | 🗣️ Speaking: 'Тестирую синтез речи' (voice: Milena)
2025-07-12 11:16:39 | INFO     | __main__:test_tts:38 | ✅ Speech completed successfully
2025-07-12 11:16:40 | INFO     | __main__:test_tts:34 | 
--- Test 3: Speaking 'Все системы работают нормально' ---
2025-07-12 11:16:40 | INFO     | jarvis.modules.tts:speak:116 | 🗣️ Speaking: 'Все системы работают нормально' (voice: Milena)
2025-07-12 11:16:42 | INFO     | __main__:test_tts:38 | ✅ Speech completed successfully
2025-07-12 11:16:43 | INFO     | __main__:test_tts:34 | 
--- Test 4: Speaking 'Открываю Сафари' ---
2025-07-12 11:16:43 | INFO     | jarvis.modules.tts:speak:116 | 🗣️ Speaking: 'Открываю Сафари' (voice: Milena)
2025-07-12 11:16:45 | INFO     | __main__:test_tts:38 | ✅ Speech completed successfully
2025-07-12 11:16:46 | INFO     | __main__:test_tts:34 | 
--- Test 5: Speaking 'До свидания!' ---
2025-07-12 11:16:46 | INFO     | jarvis.modules.tts:speak:116 | 🗣️ Speaking: 'До свидания!' (voice: Milena)
2025-07-12 11:16:47 | INFO     | __main__:test_tts:38 | ✅ Speech completed successfully
2025-07-12 11:16:48 | INFO     | __main__:test_tts:46 | 
--- Testing different voices ---
2025-07-12 11:16:48 | INFO     | __main__:test_tts:52 | Testing voice: Milena
2025-07-12 11:16:48 | INFO     | jarvis.modules.tts:speak:116 | 🗣️ Speaking: 'Тестирую разные голоса' (voice: Milena)
2025-07-12 11:16:49 | INFO     | __main__:test_tts:55 | ✅ Voice Milena works
2025-07-12 11:16:50 | INFO     | __main__:test_tts:52 | Testing voice: Yuri
2025-07-12 11:16:50 | INFO     | jarvis.modules.tts:speak:116 | 🗣️ Speaking: 'Тестирую разные голоса' (voice: Yuri)
2025-07-12 11:16:52 | INFO     | __main__:test_tts:55 | ✅ Voice Yuri works
2025-07-12 11:16:53 | INFO     | __main__:test_tts:52 | Testing voice: Alex
2025-07-12 11:16:53 | INFO     | jarvis.modules.tts:speak:116 | 🗣️ Speaking: 'Тестирую разные голоса' (voice: Alex)
2025-07-12 11:16:54 | INFO     | __main__:test_tts:55 | ✅ Voice Alex works
2025-07-12 11:16:55 | INFO     | jarvis.modules.tts:stop_speaking:152 | 🔇 Stopped speaking
2025-07-12 11:16:55 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: TTSModule
2025-07-12 11:16:55 | INFO     | __main__:test_tts:63 | 
🏁 TTS Module test completed!
2025-07-12 11:17:06 | INFO     | __main__:initialize:27 | Initializing Jarvis AI Assistant...
2025-07-12 11:17:06 | INFO     | jarvis.modules.voice:initialize:29 | Initializing Voice Module...
2025-07-12 11:17:06 | INFO     | jarvis.modules.voice:initialize:30 | 🔄 Using fully local speech recognition
2025-07-12 11:17:06 | INFO     | jarvis.modules.voice:initialize:37 | Calibrating microphone for ambient noise...
2025-07-12 11:17:07 | INFO     | jarvis.modules.voice:initialize:40 | Voice Module initialized successfully
2025-07-12 11:17:07 | INFO     | __main__:initialize:52 | Module VoiceModule initialized successfully
2025-07-12 11:17:07 | INFO     | jarvis.modules.command_processor:initialize:98 | Initializing Local Command Processor...
2025-07-12 11:17:07 | INFO     | jarvis.modules.command_processor:initialize:100 | Local Command Processor initialized successfully
2025-07-12 11:17:07 | INFO     | __main__:initialize:52 | Module LocalCommandProcessor initialized successfully
2025-07-12 11:17:07 | INFO     | jarvis.modules.app_manager:initialize:21 | Initializing App Manager...
2025-07-12 11:17:08 | INFO     | jarvis.modules.app_manager:initialize:26 | ✅ AppleScript is available
2025-07-12 11:17:08 | INFO     | jarvis.modules.app_manager:initialize:32 | App Manager initialized successfully
2025-07-12 11:17:08 | INFO     | __main__:initialize:52 | Module AppManager initialized successfully
2025-07-12 11:17:08 | INFO     | jarvis.modules.tts:initialize:23 | Initializing TTS Module...
2025-07-12 11:17:08 | INFO     | jarvis.modules.tts:_test_say_command:54 | ✅ macOS 'say' command is available
2025-07-12 11:17:08 | INFO     | jarvis.modules.tts:_get_available_voices:87 | Available Russian voices: ['Milena']
2025-07-12 11:17:08 | INFO     | jarvis.modules.tts:_get_available_voices:88 | Available English voices: ['Albert', 'Bad', 'Bahh']...
2025-07-12 11:17:08 | INFO     | jarvis.modules.tts:_get_available_voices:100 | Selected voice: Milena
2025-07-12 11:17:08 | INFO     | jarvis.modules.tts:initialize:35 | ✅ TTS Module initialized successfully
2025-07-12 11:17:08 | INFO     | __main__:initialize:52 | Module TTSModule initialized successfully
2025-07-12 11:17:08 | INFO     | __main__:initialize:58 | Jarvis initialization complete!
2025-07-12 11:17:08 | INFO     | __main__:start:65 | Jarvis is now listening...
2025-07-12 11:17:08 | INFO     | __main__:start:66 | Say 'jarvis' to activate
2025-07-12 11:17:08 | INFO     | jarvis.modules.voice:start_listening:60 | 🎤 Started listening for wake word: 'jarvis'
2025-07-12 11:17:55 | INFO     | __main__:signal_handler:202 | Received signal 2
2025-07-12 11:17:56 | INFO     | __main__:shutdown:89 | Shutting down Jarvis...
2025-07-12 11:17:56 | INFO     | jarvis.modules.voice:stop_listening:69 | 🔇 Stopped listening
2025-07-12 11:17:56 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: VoiceModule
2025-07-12 11:17:56 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: LocalCommandProcessor
2025-07-12 11:17:56 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: AppManager
2025-07-12 11:17:56 | INFO     | jarvis.modules.tts:stop_speaking:152 | 🔇 Stopped speaking
2025-07-12 11:17:56 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: TTSModule
2025-07-12 11:17:56 | INFO     | __main__:shutdown:99 | Jarvis shutdown complete
