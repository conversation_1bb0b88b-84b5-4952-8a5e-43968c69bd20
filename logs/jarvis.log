2025-07-12 10:53:53 | INFO     | __main__:initialize:23 | Initializing Jarvis AI Assistant...
2025-07-12 10:53:53 | INFO     | __main__:initialize:47 | Jarvis initialization complete!
2025-07-12 10:53:53 | INFO     | __main__:start:54 | <PERSON> is now listening...
2025-07-12 10:53:53 | INFO     | __main__:start:55 | Say 'jarvis' to activate
2025-07-12 10:54:08 | INFO     | __main__:signal_handler:87 | Received signal 2
2025-07-12 10:54:08 | INFO     | __main__:shutdown:69 | Shutting down Jarvis...
2025-07-12 10:54:08 | INFO     | __main__:shutdown:79 | <PERSON> shutdown complete
2025-07-12 10:57:08 | INFO     | __main__:main:45 | 🚀 Starting OpenAI API test...
2025-07-12 10:57:08 | INFO     | __main__:test_openai_api:14 | 🔑 Testing OpenAI API key...
2025-07-12 10:57:08 | INFO     | __main__:test_openai_api:15 | API Key: sk-ijklqrst5678uvwxi...
2025-07-12 10:57:09 | ERROR    | __main__:test_openai_api:40 | ❌ OpenAI API test failed: Error code: 401 - {'error': {'message': 'Incorrect API key provided: sk-ijklq*******************************qrst. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-07-12 10:57:09 | ERROR    | __main__:main:52 | 💥 API test failed!
2025-07-12 10:58:24 | INFO     | __main__:initialize:24 | Initializing Jarvis AI Assistant...
2025-07-12 10:58:24 | INFO     | jarvis.modules.voice:initialize:28 | Initializing Voice Module...
2025-07-12 10:58:24 | INFO     | jarvis.modules.voice:_test_openai_connection:60 | Testing OpenAI API connection...
2025-07-12 10:58:27 | ERROR    | jarvis.modules.voice:_test_openai_connection:77 | ❌ OpenAI API connection failed: Error code: 401 - {'error': {'message': 'Incorrect API key provided: sk-ijklq*******************************qrst. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-07-12 10:58:27 | WARNING  | jarvis.modules.voice:initialize:37 | OpenAI API not available: Error code: 401 - {'error': {'message': 'Incorrect API key provided: sk-ijklq*******************************qrst. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-07-12 10:58:27 | INFO     | jarvis.modules.voice:initialize:38 | 🔄 Continuing with local speech recognition only
2025-07-12 10:58:29 | INFO     | jarvis.modules.voice:initialize:47 | Calibrating microphone for ambient noise...
2025-07-12 10:58:30 | INFO     | jarvis.modules.voice:initialize:50 | Voice Module initialized successfully
2025-07-12 10:58:30 | INFO     | __main__:initialize:46 | Module VoiceModule initialized successfully
2025-07-12 10:58:30 | INFO     | __main__:initialize:52 | Jarvis initialization complete!
2025-07-12 10:58:30 | INFO     | __main__:start:59 | Jarvis is now listening...
2025-07-12 10:58:30 | INFO     | __main__:start:60 | Say 'jarvis' to activate
2025-07-12 10:58:49 | INFO     | __main__:signal_handler:111 | Received signal 2
2025-07-12 10:58:49 | INFO     | __main__:shutdown:77 | Shutting down Jarvis...
2025-07-12 10:58:49 | INFO     | jarvis.modules.voice:stop_listening:99 | 🔇 Stopped listening
2025-07-12 10:58:49 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: VoiceModule
2025-07-12 10:58:49 | INFO     | __main__:shutdown:87 | Jarvis shutdown complete
2025-07-12 10:59:43 | INFO     | __main__:initialize:24 | Initializing Jarvis AI Assistant...
2025-07-12 10:59:43 | INFO     | jarvis.modules.voice:initialize:28 | Initializing Voice Module...
2025-07-12 10:59:43 | INFO     | jarvis.modules.voice:_test_openai_connection:61 | Testing OpenAI API connection...
2025-07-12 10:59:44 | ERROR    | jarvis.modules.voice:_test_openai_connection:78 | ❌ OpenAI API connection failed: Error code: 401 - {'error': {'message': 'Incorrect API key provided: sk-ijklq*******************************qrst. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-07-12 10:59:44 | WARNING  | jarvis.modules.voice:initialize:37 | OpenAI API not available: Error code: 401 - {'error': {'message': 'Incorrect API key provided: sk-ijklq*******************************qrst. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-07-12 10:59:44 | INFO     | jarvis.modules.voice:initialize:38 | 🔄 Continuing with local speech recognition only
2025-07-12 10:59:44 | INFO     | jarvis.modules.voice:initialize:47 | Calibrating microphone for ambient noise...
2025-07-12 10:59:45 | INFO     | jarvis.modules.voice:initialize:50 | Voice Module initialized successfully
2025-07-12 10:59:45 | INFO     | __main__:initialize:46 | Module VoiceModule initialized successfully
2025-07-12 10:59:45 | INFO     | __main__:initialize:52 | Jarvis initialization complete!
2025-07-12 10:59:45 | INFO     | __main__:start:59 | Jarvis is now listening...
2025-07-12 10:59:45 | INFO     | __main__:start:60 | Say 'jarvis' to activate
2025-07-12 10:59:45 | INFO     | jarvis.modules.voice:start_listening:91 | 🎤 Started listening for wake word: 'jarvis'
2025-07-12 11:00:00 | ERROR    | jarvis.modules.voice:_listen_loop:126 | Error in listen loop: There is no current event loop in thread 'Thread-1'.
2025-07-12 11:00:09 | ERROR    | jarvis.modules.voice:_listen_loop:126 | Error in listen loop: There is no current event loop in thread 'Thread-1'.
2025-07-12 11:00:15 | ERROR    | jarvis.modules.voice:_listen_loop:126 | Error in listen loop: There is no current event loop in thread 'Thread-1'.
2025-07-12 11:00:35 | INFO     | __main__:signal_handler:111 | Received signal 2
2025-07-12 11:00:35 | INFO     | __main__:shutdown:77 | Shutting down Jarvis...
2025-07-12 11:00:35 | INFO     | jarvis.modules.voice:stop_listening:100 | 🔇 Stopped listening
2025-07-12 11:00:35 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: VoiceModule
2025-07-12 11:00:35 | INFO     | __main__:shutdown:87 | Jarvis shutdown complete
